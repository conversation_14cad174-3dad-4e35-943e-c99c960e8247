# مشروع موقع تعليمي

## هيكلية الملفات والمجلدات

```
project-root/
│
├── index.html                # الصفحة الرئيسية
├── login.html                # صفحة تسجيل الدخول
├── register.html             # صفحة إنشاء حساب جديد
├── courses.html              # صفحة عرض جميع الدورات
├── course-details.html       # صفحة تفاصيل دورة معينة
├── dashboard.html            # لوحة تحكم المستخدم
│
├── assets/                   # ملفات ثابتة (صور، خطوط، ...)
│   ├── images/
│   └── fonts/
│
├── css/                      # ملفات الأنماط (CSS)
│   └── style.css
│
├── js/                       # ملفات البرمجة (JavaScript)
│   └── main.js
│
└── README.md                 # هذا الملف
```

يمكنك البدء بإنشاء هذه الملفات والمجلدات، ثم سنقوم لاحقًا بإضافة المحتوى والبرمجة لكل صفحة. 