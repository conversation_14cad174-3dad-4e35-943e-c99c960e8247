/* Floating Chat Widget Styles */
.chat-widget {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    font-family: 'Cairo', sans-serif;
}

.chat-widget-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(72, 206, 29, 0.3);
    transition: all 0.3s ease;
    border: 3px solid #fff;
}

.chat-widget-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(72, 206, 29, 0.4);
}

.chat-widget-icon span {
    font-size: 24px;
    color: #fff;
}

.chat-widget-container {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 350px;
    height: 500px;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    display: none;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
    border: 1px solid #e0e0e0;
}

.chat-widget-container.active {
    display: flex;
}

.chat-widget-header {
    background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
    color: #fff;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 20px 20px 0 0;
}

.chat-widget-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-widget-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.chat-widget-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.chat-widget-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.chat-widget-message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.chat-widget-message.user {
    flex-direction: row-reverse;
}

.chat-widget-message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.chat-widget-message.bot .chat-widget-message-content {
    background: #fff;
    color: #333;
    border: 1px solid #e0e0e0;
    border-radius: 18px 18px 18px 4px;
}

.chat-widget-message.user .chat-widget-message-content {
    background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
    color: #fff;
    border-radius: 18px 18px 4px 18px;
}

.chat-widget-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.chat-widget-message.bot .chat-widget-avatar {
    background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
    color: #fff;
}

.chat-widget-message.user .chat-widget-avatar {
    background: #e0e0e0;
    color: #666;
}

.chat-widget-typing {
    display: none;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.chat-widget-typing.active {
    display: flex;
}

.chat-widget-typing-dots {
    display: flex;
    gap: 4px;
}

.chat-widget-typing-dot {
    width: 8px;
    height: 8px;
    background: #48ce1d;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.chat-widget-typing-dot:nth-child(1) { animation-delay: -0.32s; }
.chat-widget-typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.chat-widget-input {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    background: #fff;
    display: flex;
    gap: 10px;
    align-items: center;
}

.chat-widget-input input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 25px;
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.chat-widget-input input:focus {
    border-color: #48ce1d;
}

.chat-widget-send {
    background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease;
    color: #fff;
    font-size: 16px;
}

.chat-widget-send:hover {
    transform: scale(1.1);
}

.chat-widget-quick-replies {
    padding: 10px 20px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.chat-widget-quick-reply {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.chat-widget-quick-reply:hover {
    background: #48ce1d;
    color: #fff;
    border-color: #48ce1d;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .chat-widget-container {
        width: calc(100vw - 40px);
        height: calc(100vh - 120px);
        bottom: 80px;
        right: 20px;
        left: 20px;
    }
    
    .chat-widget-icon {
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
    }
    
    .chat-widget-icon span {
        font-size: 20px;
    }
}

/* Animation for widget appearance */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-widget-container.active {
    animation: slideIn 0.3s ease-out;
}
