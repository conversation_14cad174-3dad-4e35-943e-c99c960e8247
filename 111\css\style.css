@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

body {
    background-color: #151413;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

main {
    margin: 0;
    padding: 0;
}

header {
    width: 100%;
    background: #191717;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    display: flex;
    align-items: center;
    justify-content: center;
    position: sticky;
    top: 0;
    z-index: 100;
    min-height: 68px;
    margin-top: 0;
    padding-top: 0;
}

.header-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    align-items: center;
    justify-content: space-between; /* بدلاً من flex-start */
    padding: 0 32px;
}

.logo {
    font-family: 'Cairo', Arial, sans-serif;
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
    margin-left: 0;
    margin-right: 4px;
}
.logo-title {
    font-size: 2rem;
    font-weight: 900;
    letter-spacing: 1px;
    color: #48ce1d;
    line-height: 1.1;
}
.logo-sub {
    font-size: 1.05rem;
    font-weight: 400;
    color: #fff;
    opacity: 0.85;
    margin-top: 2px;
    letter-spacing: 0.5px;
}
.logo .highlight {
    color: #48ce1d;
}
.logo .edu-icon {
    width: 38px;
    height: 38px;
    display: inline-block;
    vertical-align: middle;
}

.logo-img {
    height: 54px;
    width: auto;
    display: block;
}

.nav {
    display: flex;
    gap: 6px;
    list-style: none;
    margin: 0;
    padding: 0;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    font-size: 0.65rem;
    align-items: center;
    margin-right: 0;
    margin-left: 18px;
}

.spacer {
    flex: 1 1 auto;
}

.nav li a {
    color: #fff;
    text-decoration: none;
    font-size: 0.89rem;
    font-weight: 500;
    padding: 7px 18px;
    border-radius: 22px;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    display: inline-block;
    position: relative;
}

.nav li a:hover, .nav li a:focus {
    background: #48ce1d;
    color: #191717;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    text-decoration: none;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-right: 0;
    margin-right: 18px;
}

.action-btn {
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.7rem;
    font-weight: 300;
    padding: 7px 20px;
    border: none;
    border-radius: 22px;
    background: #48ce1d;
    color: #191717;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, border 0.2s;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    margin-left: 4px;
    letter-spacing: 1px;
}
.action-btn.subscribe {
    background: #3eff00;
    color: #191717;
    border-radius: 8px;
    font-weight: 300;
    box-shadow: 0 2px 8px rgba(62, 255, 0, 0.10);
}
.action-btn.subscribe:hover, .action-btn.subscribe:focus {
    background: #2ecc00;
    color: #fff;
}
.action-btn.login {
    background: transparent;
    border: 2px solid #fff;
    color: #fff;
    border-radius: 8px;
    font-weight: 300;
}
.action-btn.login:hover, .action-btn.login:focus {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-color: #3eff00;
}

.action-btn.close-btn {
    background: #ff4444;
    color: #fff;
    border-radius: 8px;
    font-weight: 300;
    box-shadow: 0 2px 8px rgba(255, 68, 68, 0.10);
}
.action-btn.close-btn:hover, .action-btn.close-btn:focus {
    background: #cc3333;
    color: #fff;
}

.cart-icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.2s;
}
.cart-icon:hover {
    color: #48ce1d;
}

.menu-btn {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.2s, background 0.2s;
    z-index: 1002;
    /* إزالة أي خلفية أو دائرة */
    border-radius: 0;
    width: auto;
    height: auto;
}
.menu-btn:hover, .menu-btn:focus {
    color: #3eff00;
    background: none;
}

@media (max-width: 900px) {
    .header-container {
        padding: 0 12px;
    }
    .nav {
        gap: 12px;
        margin-right: 8px;
    }
}

.mobile-menu {
    display: flex !important;
    flex-direction: column;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    width: 80vw;
    max-width: 320px;
    height: auto;
    max-height: 100vh;
    background: #191717;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    z-index: 1001;
    padding: 32px 18px 18px 18px;
    gap: 18px;
    align-items: flex-start;
    transition: transform 0.25s cubic-bezier(.4,0,.2,1);
    pointer-events: none;
    opacity: 0;
    padding-bottom: 1cm;
}
.mobile-menu.active {
    transform: translate(-50%, -50%) scale(1);
    pointer-events: auto;
    opacity: 1;
}
@media (max-width: 700px) {
    .nav {
        display: none;
    }
    /* لا تخفي .header-actions بالكامل */
    .menu-btn {
        display: block !important;
        position: static;
        right: auto;
        top: auto;
        color: #fff;
        background: none;
        border-radius: 0;
        width: auto;
        height: auto;
        font-size: 2.2rem;
        z-index: 2000;
        box-shadow: none;
        border: none;
        margin-right: 0;
        margin-left: 0;
    }
    .menu-btn:hover, .menu-btn:focus {
        color: #3eff00;
        background: none;
    }
    .logo {
        margin-right: 0;
    }
    .mobile-menu {
        display: flex !important;
    }
    .mobile-menu .nav,
    .mobile-menu .header-actions {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 12px;
        margin: 0;
        align-items: stretch;
    }
    .mobile-menu .nav li a {
        width: 100%;
        text-align: right;
        padding: 12px 0;
        font-size: 1.1rem;
        border-radius: 0;
        background: none;
        color: #fff;
    }
    .mobile-menu .action-btn {
        width: 100%;
        margin: 0 0 8px 0;
        font-size: 1.1rem;
    }
    .mobile-menu .action-btn.subscribe {
        border-radius: 8px;
    }
    .mobile-menu .login-btn-links {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        background: transparent;
        border: 2px solid #fff;
        color: #fff !important;
        border-radius: 8px;
        font-family: 'Cairo', Arial, sans-serif;
        font-size: 1.1rem;
        font-weight: 300;
        padding: 7px 20px;
        margin-bottom: 8px;
        text-align: center;
        transition: background 0.2s, color 0.2s, border 0.2s;
        box-sizing: border-box;
        min-height: auto;
    }
    .mobile-menu .login-btn-links:hover, .mobile-menu .login-btn-links:focus {
        background: rgba(255,255,255,0.08);
        color: #fff !important;
        border-color: #3eff00;
        text-decoration: none;
    }
}

/* التجاوب للهاتف والتابلت */
@media (max-width: 768px) {
    .header-container {
        padding: 0 20px;
    }
    
    main section {
        flex-direction: column !important;
        padding: 20px !important;
        text-align: center;
    }
    
    main h1 {
        font-size: 2rem !important;
        white-space: normal !important;
        margin-bottom: 20px !important;
        order: 2;
    }
    
    main p {
        font-size: 1rem !important;
        margin-bottom: 30px !important;
        order: 3;
    }
    
    main img {
        width: 250px !important;
        height: 250px !important;
        margin-top: 0 !important;
        order: 1;
        display: none !important;
    }
    
    main .buttons-container {
        flex-direction: column !important;
        gap: 15px !important;
        order: 4;
        align-items: center !important;
        justify-content: center !important;
    }
    
    main button {
        width: 100% !important;
        max-width: 300px !important;
    }
}

/* لماذا منصة دارين */
.why-dareen-section {
    background: #181818;
    padding: 36px 0 40px 0;
    margin: 0;
    text-align: center;
}
.why-dareen-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}
.why-dareen-title {
    font-family: 'Cairo', Arial, sans-serif;
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
    margin: 0 auto 18px auto;
    background: none;
    text-align: center;
}
.why-dareen-title::after {
    content: '';
    display: block;
    position: absolute;
    left: 10%;
    right: 10%;
    bottom: 0;
    height: 18px;
    background: linear-gradient(90deg, #48ce1d 80%, #a6f7a1 100%);
    border-radius: 8px 8px 16px 16px;
    opacity: 0.55;
    z-index: -1;
}
.why-dareen-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
    gap: 28px;
}
.why-dareen-card {
    background: #202020;
    border-radius: 18px;
    padding: 36px 18px 30px 18px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0,0,0,0.09);
    transition: transform 0.18s;
    border: 1.5px solid #232323;
}
.why-dareen-card:hover {
    transform: none;
    box-shadow: 0 2px 12px rgba(0,0,0,0.09);
    border-color: #48ce1d;
}
.why-dareen-icon {
    font-size: 2.7rem;
    margin-bottom: 18px;
    color: #48ce1d;
    display: block;
}
.why-dareen-card-title {
    color: #fff;
    font-size: 1.13rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 0.2px;
}
.why-dareen-card-desc {
    color: #cfcfcf;
    font-size: 0.91rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 300;
    line-height: 1.5;
    margin: 0;
}
@media (max-width: 700px) {
    .why-dareen-section {
        padding: 36px 0 20px 0;
    }
    .why-dareen-title {
        font-size: 1.3rem;
        margin-bottom: 28px;
    }
    .why-dareen-grid {
        gap: 16px;
    }
    .why-dareen-card {
        padding: 22px 8px 18px 8px;
        border-radius: 12px;
    }
    .why-dareen-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    .why-dareen-card-title {
        font-size: 1rem;
    }
    .why-dareen-card-desc {
        font-size: 0.93rem;
    }
    .why-dareen-desc {
        white-space: nowrap;
        overflow-x: auto;
        text-overflow: ellipsis;
        font-size: 0.82rem;
    }
}

/* أحدث الكورسات في المنصة */
.latest-courses-section {
    background: #181818;
    padding: 54px 0 40px 0;
    margin: 0;
}
.latest-courses-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}
.latest-courses-title {
    color: #48ce1d;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    text-align: center;
    margin-bottom: 44px;
    letter-spacing: 0.5px;
}
.latest-courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 32px;
}
.course-card {
    position: relative;
    background: linear-gradient(135deg, #232323 80%, #191919 100%);
    border-radius: 16px;
    padding: 0 0 22px 0;
    text-align: center;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-height: 340px;
    max-width: 340px;
    margin: 0 auto;
    overflow: hidden;
}
.course-card::before {
    content: "";
    position: absolute;
    top: 32px;
    right: 18px;
    width: 48px;
    height: 48px;
    background: rgba(72, 206, 29, 0.10);
    border-radius: 50%;
    z-index: 0;
}
.course-card::after {
    content: "";
    position: absolute;
    bottom: 18px;
    left: 22px;
    width: 28px;
    height: 28px;
    background: rgba(72, 206, 29, 0.18);
    border-radius: 50%;
    z-index: 0;
}
.course-card .circle-decor1 {
    position: absolute;
    top: 60px;
    left: 30px;
    width: 22px;
    height: 22px;
    background: rgba(72, 206, 29, 0.13);
    border-radius: 50%;
    z-index: 0;
}
.course-card .circle-decor2 {
    position: absolute;
    bottom: 38px;
    right: 38px;
    width: 18px;
    height: 18px;
    background: rgba(72, 206, 29, 0.09);
    border-radius: 50%;
    z-index: 0;
}
.course-card .circle-decor3 {
    position: absolute;
    top: 50%;
    left: 60%;
    width: 14px;
    height: 14px;
    background: rgba(72, 206, 29, 0.15);
    border-radius: 50%;
    z-index: 0;
    transform: translate(-50%, -50%);
}
.course-card:hover {
    box-shadow: 0 12px 36px 0 rgba(72,206,29,0.18), 0 2px 16px 0 rgba(0,0,0,0.13);
    background: linear-gradient(120deg, #232323 60%, #232323 80%, #232323 90%, #232323 100%, #2ecc00 120%);
    transition: box-shadow 0.25s, background 0.25s;
}
.course-img-placeholder {
    width: 94%;
    min-height: 150px;
    background: #181818;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #191717;
    font-size: 1.18rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 10px auto 7px auto;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}
.course-img-placeholder::before {
    content: "";
    position: absolute;
    top: 18px;
    left: 18px;
    width: 38px;
    height: 38px;
    background: rgba(72, 206, 29, 0.13);
    border-radius: 50%;
    z-index: 1;
}
.course-img-placeholder::after {
    content: "";
    position: absolute;
    bottom: 14px;
    right: 22px;
    width: 22px;
    height: 22px;
    background: rgba(72, 206, 29, 0.22);
    border-radius: 50%;
    z-index: 1;
}
.course-img-real {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 14px;
    display: block;
}
.course-card-title {
    color: #fff;
    font-size: 0.76rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 18px 0 22px 0;
    letter-spacing: 0.2px;
    background: transparent;
    border: 1.5px solid #48ce1d;
    border-radius: 0;
    padding: 5px 13px 4px 13px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.07);
    transition: border-color 0.2s, box-shadow 0.2s;
    text-align: right;
    float: right;
    margin-right: 0;
}
.course-card-title:hover, .course-card-title:focus {
    border-color: #3eff00;
    box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
}
.course-card-desc {
    color: #cfcfcf;
    font-size: 0.97rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.7;
    margin: 0 0 18px 0;
    min-height: 48px;
    clear: both;
    margin-top: 10px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    word-break: break-word;
}
.course-card-btn {
    width: 94%;
    border-radius: 0;
    background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%);
    color: #191717;
    border: none;
    padding: 8px 0;
    border-radius: 24px;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.85rem;
    font-weight: 700;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    margin-top: auto;
    width: 85%;
    max-width: 160px;
    display: block;
}
.course-card-btn:hover {
    background: linear-gradient(90deg, #2ecc00 60%, #48ce1d 100%);
    color: #fff;
}
.course-badge {
    position: absolute;
    top: 26px;
    left: 8%;
    right: auto;
    background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%);
    color: #191717;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.85rem;
    font-weight: 700;
    padding: 4px 16px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    z-index: 2;
    letter-spacing: 0.5px;
}
.course-card {
    position: relative;
}
.course-card-meta {
    display: flex;
    justify-content: center;
    gap: 18px;
    margin-bottom: 10px;
    margin-top: -8px;
}
.course-meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #48ce1d;
    font-size: 0.93rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 600;
}
.course-card-price {
    color: #191717;
    background: #48ce1d;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 1.01rem;
    font-weight: 700;
    border-radius: 12px;
    padding: 5px 18px;
    display: inline-block;
    margin-bottom: 10px;
    margin-top: 0;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
}
.course-card-sub {
    color: #48ce1d;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.97rem;
    font-weight: 700;
    margin: 0 0 6px 0;
    text-align: center;
    letter-spacing: 0.2px;
}
.course-card-mini-btn {
    background: #232323;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
    padding: 6px 18px;
    margin-bottom: 8px;
    margin-top: 0;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    box-shadow: 0 1px 4px rgba(72, 206, 29, 0.08);
    display: inline-block;
}
.course-card-mini-btn:hover {
    background: #48ce1d;
    color: #191717;
}
.course-card-subscribers {
    display: flex;
    align-items: center;
    gap: 2px;
    background: rgba(72, 206, 29, 0.10);
    color: #48ce1d;
    font-size: 0.81rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    border-radius: 8px;
    padding: 1px 7px 1px 5px;
    margin-right: 0;
    height: 22px;
}
.course-card-subscribers svg {
    width: 12px;
    height: 12px;
    margin-left: 2px;
    color: #ff4444;
    opacity: 0.95;
}
.course-img-placeholder {
    width: 94%;
    min-height: 150px;
    background: #181818;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #191717;
    font-size: 1.18rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 10px auto 7px auto;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}
.course-card-subscribers {
    position: absolute;
    left: 10px;
    bottom: 10px;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 2px;
    background: rgba(72, 206, 29, 0.10);
    color: #48ce1d;
    font-size: 0.81rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    border-radius: 8px;
    padding: 1px 7px 1px 5px;
    margin-right: 0;
    height: 22px;
}
@media (max-width: 700px) {
    .course-img-placeholder {
        width: 100% !important;
        min-height: 75px;
        font-size: 0.98rem;
        border-radius: 10px;
        margin: 6px auto 5px auto;
    }
    .course-img-real {
        height: 75px;
        border-radius: 10px;
    }
    .course-card-btn {
        width: 100% !important;
        max-width: none !important;
        min-width: 0 !important;
        font-size: 0.93rem;
        margin-top: auto;
    }
    .course-badge {
        top: 14px;
        left: 4%;
        right: auto;
        font-size: 0.75rem;
        padding: 3px 10px;
        border-radius: 12px;
    }
    .course-card-meta {
        gap: 8px;
        font-size: 0.81rem;
    }
    .course-card-price {
        font-size: 0.91rem;
        padding: 4px 10px;
        border-radius: 8px;
    }
    .course-card-subscribers {
        font-size: 0.72rem;
        height: 18px;
        padding: 1px 5px 1px 3px;
        left: 5px;
        bottom: 5px;
    }
    .course-card {
        height: 260px !important;
        min-height: unset !important;
        max-height: 260px !important;
        display: flex !important;
        flex-direction: column !important;
    }
    .latest-courses-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 16px !important;
        align-items: stretch !important;
    }
    .course-card {
        height: 100% !important;
        min-height: 220px !important;
        display: flex !important;
        flex-direction: column !important;
    }
    .course-img-placeholder {
        flex-shrink: 0;
    }
    .course-card-btn {
        margin-top: auto;
    }
}

.course-card-price-group {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #232323;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 auto 10px auto;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.07);
    width: max-content;
}
.price-part {
    color: #48ce1d;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.93rem;
    font-weight: 700;
    padding: 6px 16px;
    background: transparent;
    border-left: 1.5px solid #333;
    display: flex;
    align-items: center;
    justify-content: center;
}
.price-part:first-child {
    border-right: none;
    border-left: 1.5px solid #333;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}
.price-part:last-child {
    border-left: none;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}
@media (max-width: 700px) {
    .course-card-price-group {
        width: 100%;
        font-size: 0.81rem;
    }
    .price-part {
        padding: 5px 7px;
        font-size: 0.81rem;
    }
}

.section-divider {
    width: 100%;
    height: 40px;
    overflow: hidden;
    background: transparent;
    margin: 0;
    padding: 0;
    line-height: 0;
}

.curriculums-section {
    background: #181818;
    padding: 54px 0 40px 0;
    margin: 0;
}
.curriculums-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}
.curriculums-title {
    color: #48ce1d;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    text-align: center;
    margin-bottom: 44px;
    letter-spacing: 0.5px;
}
.curriculums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 32px;
}
.curriculum-card {
    position: relative;
    background: linear-gradient(135deg, #232323 80%, #191919 100%);
    border-radius: 16px;
    padding: 0 0 22px 0;
    text-align: center;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-height: 340px;
    max-width: 340px;
    margin: 0 auto;
    overflow: hidden;
}
.curriculum-img-placeholder {
    width: 94%;
    min-height: 150px;
    background: #181818;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #191717;
    font-size: 1.18rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 10px auto 7px auto;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}
.curriculum-img-real {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 14px;
    display: block;
}
.curriculum-card-title {
    color: #fff;
    font-size: 1.13rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 18px 0 10px 0;
    letter-spacing: 0.2px;
    background: transparent;
    border: 1.5px solid #48ce1d;
    border-radius: 0;
    padding: 5px 13px 4px 13px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.07);
    transition: border-color 0.2s, box-shadow 0.2s;
    text-align: right;
    float: right;
    margin-right: 0;
}
.curriculum-card-title:hover, .curriculum-card-title:focus {
    border-color: #3eff00;
    box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
}
.curriculum-card-desc {
    color: #cfcfcf;
    font-size: 0.97rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.7;
    margin: 0 0 18px 0;
    min-height: 48px;
    clear: both;
    margin-top: 10px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    word-break: break-word;
}
.curriculum-card-btn {
    width: 94%;
    border-radius: 0;
    background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%);
    color: #191717;
    border: none;
    padding: 8px 0;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.85rem;
    font-weight: 700;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    margin-top: auto;
    max-width: 160px;
    display: block;
}
.curriculum-card-btn:hover {
    background: linear-gradient(90deg, #2ecc00 60%, #48ce1d 100%);
    color: #fff;
}
@media (max-width: 700px) {
    .curriculums-grid {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }
    .curriculum-img-placeholder {
        width: 100% !important;
        min-height: 75px;
        font-size: 0.98rem;
        border-radius: 10px;
        margin: 6px auto 5px auto;
    }
    .curriculum-img-real {
        height: 75px;
        border-radius: 10px;
    }
    .curriculum-card-btn {
        width: 100% !important;
        max-width: none !important;
        min-width: 0 !important;
        font-size: 0.93rem;
        margin-top: auto;
    }
    .curriculum-card {
        min-height: 220px !important;
        display: flex !important;
        flex-direction: column !important;
    }
}

.teachers-reviews-section {
    background: #181818;
    padding: 54px 0 40px 0;
    margin: 0;
}
.teachers-reviews-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}
.teachers-reviews-title {
    color: #48ce1d;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    text-align: center;
    margin-bottom: 44px;
    letter-spacing: 0.5px;
}
.teachers-reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 32px;
}
.teacher-review-card {
    background: linear-gradient(135deg, #232323 80%, #191919 100%);
    border-radius: 16px;
    padding: 32px 18px 24px 18px;
    text-align: center;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-height: 320px;
    max-width: 340px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.teacher-avatar {
    width: 74px;
    height: 74px;
    border-radius: 50%;
    background: #222;
    margin-bottom: 18px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #48ce1d;
}
.teacher-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}
.teacher-review-name {
    color: #fff;
    font-size: 1.13rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin-bottom: 4px;
    letter-spacing: 0.2px;
}
.teacher-review-role {
    color: #48ce1d;
    font-size: 0.97rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 600;
    margin-bottom: 12px;
}
.teacher-review-text {
    color: #cfcfcf;
    font-size: 0.97rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.7;
    margin: 0 0 18px 0;
    min-height: 48px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    word-break: break-word;
}
@media (max-width: 700px) {
    .teachers-reviews-grid {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }
    .teacher-review-card {
        min-height: 180px !important;
        padding: 18px 8px 16px 8px;
    }
    .teacher-avatar {
        width: 54px;
        height: 54px;
        margin-bottom: 10px;
    }
    .teacher-review-name {
        font-size: 1rem;
    }
    .teacher-review-role {
        font-size: 0.91rem;
    }
    .teacher-review-text {
        font-size: 0.93rem;
    }
}

.parents-reviews-section {
    background: #181818;
    padding: 40px 0 30px 0;
    margin: 0;
}
.parents-reviews-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}
.parents-reviews-title {
    color: #48ce1d;
    font-size: 2rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    text-align: center;
    margin-bottom: 32px;
    letter-spacing: 0.5px;
}
.parents-reviews-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 18px;
}
@media (max-width: 900px) {
    .parents-reviews-grid {
        grid-template-columns: 1fr 1fr !important;
        gap: 10px !important;
    }
}
.parents-slider-wrapper {
    width: 100%;
    overflow: hidden;
    margin: 0 auto;
    padding: 0;
    display: flex;
    justify-content: center;
}
.parents-slider {
    display: flex;
    gap: 18px;
    width: 100%;
    align-items: stretch;
    justify-content: center;
}
.parent-review-card {
    background: linear-gradient(135deg, #232323 80%, #191919 100%);
    border-radius: 12px;
    padding: 18px 12px 14px 12px;
    text-align: center;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.10);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-width: 220px;
    max-width: 240px;
    min-height: 80px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.parent-review-name {
    color: #fff;
    font-size: 1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin-bottom: 6px;
    letter-spacing: 0.2px;
}
.parent-review-text {
    color: #cfcfcf;
    font-size: 0.93rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.5;
    margin: 0 0 6px 0;
    min-height: 32px;
    width: 95%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    word-break: break-word;
}
@media (max-width: 900px) {
    .parents-slider {
        gap: 8px;
    }
    .parent-review-card {
        min-width: 90vw;
        max-width: 98vw;
        padding: 12px 4px 10px 4px;
    }
}

.quran-section {
    background: #181818;
    padding: 54px 0 40px 0;
    margin: 0;
}
.quran-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}
.quran-title {
    color: #48ce1d;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    text-align: center;
    margin-bottom: 44px;
    letter-spacing: 0.5px;
}
.quran-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 32px;
}
.quran-card {
    background: linear-gradient(135deg, #232323 80%, #191919 100%);
    border-radius: 16px;
    padding: 32px 18px 24px 18px;
    text-align: center;
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.10);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-height: 180px;
    max-width: 340px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.quran-card-title {
    color: #fff;
    font-size: 1.13rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 0.2px;
    background: transparent;
    border: 1.5px solid #48ce1d;
    border-radius: 0;
    padding: 5px 13px 4px 13px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.07);
    transition: border-color 0.2s, box-shadow 0.2s;
    text-align: right;
    float: right;
    margin-right: 0;
}
.quran-card-title:hover, .quran-card-title:focus {
    border-color: #3eff00;
    box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
}
.quran-card-desc {
    color: #cfcfcf;
    font-size: 0.97rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.7;
    margin: 0 0 18px 0;
    min-height: 48px;
    clear: both;
    margin-top: 10px;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    word-break: break-word;
}
.quran-card-btn {
    width: 94%;
    border-radius: 0;
    background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%);
    color: #191717;
    border: none;
    padding: 8px 0;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 0.95rem;
    font-weight: 700;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    margin-top: auto;
    max-width: 160px;
    display: block;
}
.quran-card-btn:hover {
    background: linear-gradient(90deg, #2ecc00 60%, #48ce1d 100%);
    color: #fff;
}
@media (max-width: 700px) {
    .quran-grid {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }
    .quran-card {
        min-height: 120px !important;
        padding: 18px 8px 12px 8px;
        max-width: 100%;
    }
    .quran-card-title {
        font-size: 1rem;
    }
    .quran-card-desc {
        font-size: 0.91rem;
    }
    .quran-card-btn {
        width: 100% !important;
        max-width: none !important;
        min-width: 0 !important;
        font-size: 0.93rem;
        margin-top: auto;
    }
}

.quran-hero-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 120px 80px 120px;
    margin: 0;
    max-width: 1400px;
    margin: 0 auto;
}
@media (max-width: 900px) {
    .quran-hero-section {
        flex-direction: column !important;
        padding: 20px 0 30px 0 !important;
        text-align: center;
        align-items: center !important;
        justify-content: center !important;
    }
    .quran-hero-section img {
        width: 400px;
        height: 400px;
        object-fit: cover;
        margin-top: 50px;
    }
    .quran-hero-section > div {
        max-width: 100% !important;
        margin-top: 0 !important;
    }
}
.quran-hero-section img {
    width: 400px;
    height: 400px;
    object-fit: cover;
    margin-top: 50px;
    display: block !important;
}
@media (max-width: 900px) {
    .quran-hero-section img {
        width: 95vw !important;
        max-width: 95vw !important;
        height: auto !important;
        margin: 0 0 18px 0 !important;
        display: block !important;
    }
}

.social-sidebar {
    position: fixed;
    top: 50%;
    left: 18px;
    right: auto;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 16px;
    z-index: 2000;
}
.social-icon {
    background: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
    border: none;
    margin: 0;
    padding: 0;
    text-decoration: none;
}
.social-icon:hover {
    transform: scale(1.08) translateY(-2px);
}
.social-img {
    width: 38px;
    height: 38px;
    object-fit: contain;
    display: block;
    background: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}
.social-icon.instagram svg rect,
.social-icon.instagram svg path,
.social-icon.instagram svg circle {
    transition: fill 0.2s, stroke 0.2s;
}
.social-icon.instagram:hover svg rect {
    fill: #E1306C;
}
.social-icon.instagram:hover svg path,
.social-icon.instagram:hover svg circle {
    stroke: #fff;
    fill: #fff;
}
.social-icon.tiktok:hover svg rect {
    fill: #000;
}
.social-icon.tiktok:hover svg path {
    stroke: #fff;
}
.social-icon.tiktok:hover svg path[stroke="#25F4EE"] {
    stroke: #25F4EE;
}
.social-icon.tiktok:hover svg path[stroke="#FE2C55"] {
    stroke: #FE2C55;
}
.social-icon.telegram:hover svg rect {
    fill: #229ED9;
}
.social-icon.telegram:hover svg path {
    stroke: #fff;
}
@media (max-width: 700px) {
    .social-sidebar {
        left: 6px;
        right: auto;
        top: auto;
        bottom: 18px;
        transform: none;
        flex-direction: row;
        gap: 10px;
    }
    .social-sidebar {
        display: none !important;
    }
}

.subjects-section {
    background: #f3f3f3;
    padding: 54px 0 54px 0;
    margin: 0;
    text-align: center;
}
.subjects-container {
    max-width: 700px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.subjects-icon {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.subjects-title {
    color: #222;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    margin-bottom: 18px;
    letter-spacing: 0.5px;
    text-align: center;
}
.subjects-desc {
    color: #444;
    font-size: 1.13rem;
    font-family: 'Noto Kufi Arabic', Arial, sans-serif;
    font-weight: 400;
    line-height: 1.7;
    margin: 0 auto;
    text-align: center;
    max-width: 600px;
}
@media (max-width: 700px) {
    .subjects-section {
        padding: 32px 0 32px 0;
    }
    .subjects-title {
        font-size: 1.3rem;
        margin-bottom: 12px;
    }
    .subjects-desc {
        font-size: 0.98rem;
    }
    .subjects-icon svg {
        width: 54px;
        height: 54px;
    }
}

.books-section {
    background: #f3f3f3;
    padding: 54px 0 54px 0;
    margin: 0;
    text-align: center;
}
.books-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.books-title {
    color: #222;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    margin-bottom: 38px;
    letter-spacing: 0.5px;
    text-align: center;
}
.books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 32px;
    width: 100%;
    justify-items: center;
}
.book-card {
    background: #fff;
    border-radius: 18px;
    padding: 18px 8px 12px 8px;
    text-align: center;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-width: 160px;
    max-width: 200px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.book-img-placeholder {
    width: 120px;
    height: 120px;
    background: #f3f3f3;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 14px auto;
    overflow: hidden;
}
.book-img-real {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    display: block;
}
.book-card-title {
    color: #222;
    font-size: 1.01rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 0;
    letter-spacing: 0.2px;
    background: transparent;
    text-align: center;
}
@media (max-width: 700px) {
    .books-section {
        padding: 32px 0 32px 0;
    }
    .books-title {
        font-size: 1.3rem;
        margin-bottom: 18px;
    }
    .books-grid {
        gap: 14px;
    }
    .book-img-placeholder {
        width: 80px;
        height: 80px;
        border-radius: 8px;
    }
    .book-card {
        min-width: 100px;
        max-width: 100%;
        padding: 10px 2px 8px 2px;
        border-radius: 10px;
    }
    .book-card-title {
        font-size: 0.91rem;
    }
}

.books-section-demo {
    background: #f3f3f3;
    padding: 54px 0 54px 0;
    margin: 0;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}
.book-card-demo {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
    padding: 22px 18px 18px 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 180px;
    min-width: 180px;
    max-width: 180px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.book-img-demo {
    width: 92px;
    height: 92px;
    background: #f3f3f3;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 18px auto;
    overflow: hidden;
}
.book-img-real-demo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    display: block;
}
.book-title-demo {
    color: #222;
    font-size: 1.05rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 0;
    letter-spacing: 0.2px;
    background: transparent;
    text-align: center;
}
@media (max-width: 700px) {
    .books-section-demo {
        padding: 32px 0 32px 0;
    }
    .book-card-demo {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        padding: 12px 6px 10px 6px;
        border-radius: 8px;
    }
    .book-img-demo {
        width: 60px;
        height: 60px;
        border-radius: 6px;
        margin-bottom: 10px;
    }
    .book-title-demo {
        font-size: 0.91rem;
    }
}

.latest-courses-section-eduvalu {
    background: #f3f3f3;
    padding: 54px 0 54px 0;
    margin: 0;
    text-align: center;
}
.latest-courses-container-eduvalu {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}
.latest-courses-title-eduvalu {
    color: #222;
    font-size: 2.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 800;
    margin-bottom: 38px;
    letter-spacing: 0.5px;
    text-align: center;
}
.latest-courses-grid-eduvalu {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 32px;
    width: 100%;
    justify-items: center;
}
.course-card-eduvalu {
    background: #fff;
    border-radius: 18px;
    padding: 0 0 22px 0;
    text-align: center;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.2s;
    min-width: 220px;
    max-width: 260px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}
.course-img-placeholder-eduvalu {
    width: 100%;
    min-height: 150px;
    background: #f3f3f3;
    border-radius: 18px 18px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0 auto;
    overflow: hidden;
}
.course-img-real-eduvalu {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 18px 18px 0 0;
    display: block;
}
.course-card-title-eduvalu {
    color: #222;
    font-size: 1.13rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 700;
    margin: 18px 0 10px 0;
    letter-spacing: 0.2px;
    background: transparent;
    text-align: center;
}
.course-card-meta-eduvalu {
    display: flex;
    justify-content: center;
    gap: 18px;
    margin-bottom: 10px;
    margin-top: 0;
}
.course-meta-item-eduvalu {
    color: #48ce1d;
    font-size: 0.93rem;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 600;
}
.course-card-price-eduvalu {
    color: #191717;
    background: #48ce1d;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 1.01rem;
    font-weight: 700;
    border-radius: 12px;
    padding: 5px 18px;
    display: inline-block;
    margin-bottom: 10px;
    margin-top: 0;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
}
.course-card-btn-eduvalu {
    background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%);
    color: #191717;
    border: none;
    padding: 13px 0;
    border-radius: 24px;
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 1.01rem;
    font-weight: 700;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(72, 206, 29, 0.10);
    margin-top: 8px;
    width: 85%;
    max-width: 200px;
    display: block;
}
.course-card-btn-eduvalu:hover {
    background: linear-gradient(90deg, #2ecc00 60%, #48ce1d 100%);
    color: #fff;
}
@media (max-width: 700px) {
    .latest-courses-section-eduvalu {
        padding: 32px 0 32px 0;
    }
    .latest-courses-title-eduvalu {
        font-size: 1.3rem;
        margin-bottom: 18px;
    }
    .latest-courses-grid-eduvalu {
        gap: 14px;
    }
    .course-img-placeholder-eduvalu {
        min-height: 90px;
        border-radius: 10px 10px 0 0;
    }
    .course-img-real-eduvalu {
        height: 90px;
        border-radius: 10px 10px 0 0;
    }
    .course-card-eduvalu {
        min-width: 100px;
        max-width: 100%;
        padding: 0 0 10px 0;
        border-radius: 10px;
    }
    .course-card-title-eduvalu {
        font-size: 0.91rem;
        margin: 10px 0 6px 0;
    }
    .course-card-price-eduvalu {
        font-size: 0.91rem;
        padding: 4px 10px;
        border-radius: 8px;
    }
    .course-card-btn-eduvalu {
        font-size: 0.93rem;
        padding: 9px 0;
        border-radius: 16px;
        max-width: 100%;
    }
}

.foundation-courses-section {
    background: #181818;
    padding: 54px 0 54px 0;
    margin: 0;
    text-align: center;
}
.foundation-courses-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.foundation-courses-title {
    color: #fff !important;
    font-size: 2.3rem !important;
    font-family: 'Cairo', Arial, sans-serif !important;
    font-weight: 900 !important;
    margin-bottom: 32px !important;
    margin-top: 0 !important;
    letter-spacing: 1.2px;
    text-align: center;
    text-shadow: 0 3px 18px #19171799, 0 1.5px 4px #48ce1d33;
    background: none !important;
    border: none !important;
    padding: 0 0 8px 0 !important;
    display: block;
}
.foundation-courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 32px;
    width: 100%;
    justify-items: center;
}
.foundation-course-card {
    background: #181818;
    color: #fff;
}
.foundation-course-name, .foundation-course-details, .rating-number {
    color: #fff !important;
}
.foundation-course-card {
    background: #181818;
    color: #fff;
}
@media (max-width: 700px) {
    .foundation-courses-section {
        padding: 32px 0 32px 0;
    }
    .foundation-courses-title {
        font-size: 1.3rem;
        margin-bottom: 18px;
    }
    .foundation-courses-grid {
        grid-template-columns: 1fr !important;
        gap: 32px !important;
    }
    .foundation-course-card {
        /* إزالة التكبير */
        transform: none !important;
        min-width: 0;
        max-width: 100%;
        padding: 20px 8px 18px 8px;
        border-radius: 12px;
        margin-bottom: 24px;
    }
    .foundation-course-img {
        max-width: 100%;
        border-radius: 8px;
        margin-bottom: 16px;
        height: auto;
        aspect-ratio: 16/9;
    }
    .foundation-course-img img {
        border-radius: 8px;
    }
    .foundation-course-name {
        font-size: 1.25rem;
    }
    .foundation-course-details {
        font-size: 1.1rem;
    }
    .foundation-course-btn {
        font-size: 1.2rem;
        padding: 14px 0;
        border-radius: 18px;
        max-width: 100%;
    }
    .star {
        font-size: 1.5rem;
    }
    .rating-number {
        font-size: 1.2rem;
    }
}

/* تطوير تصميم كورسات التأسيس */
.foundation-course-card {
  background: rgba(24, 24, 24, 0.75);
  border-radius: 28px !important;
  box-shadow: 0 1.5px 6px 0 rgba(72, 206, 29, 0.07), 0 1px 4px 0 rgba(0,0,0,0.10);
  border: 1.5px solid rgba(72, 206, 29, 0.18);
  backdrop-filter: blur(6px);
  position: relative;
  overflow: visible;
  transition: box-shadow 0.18s;
}
.foundation-course-card:hover {
  transform: none;
  box-shadow: 0 2px 8px 0 rgba(72,206,29,0.10), 0 1.5px 6px 0 rgba(0,0,0,0.10);
  border-color: #48ce1d;
}
.foundation-course-card::before {
  content: "";
  position: absolute;
  top: -18px;
  right: -18px;
  width: 48px;
  height: 48px;
  background: rgba(72, 206, 29, 0.10);
  border-radius: 50%;
  z-index: 0;
}
.foundation-course-card::after {
  content: "";
  position: absolute;
  bottom: -18px;
  left: -18px;
  width: 38px;
  height: 38px;
  background: rgba(72, 206, 29, 0.13);
  border-radius: 50%;
  z-index: 0;
}
.foundation-course-card:hover {
  transform: scale(1.045) translateY(-6px);
  box-shadow: 0 16px 48px 0 rgba(72,206,29,0.18), 0 4px 24px 0 rgba(0,0,0,0.18);
  border-color: #48ce1d;
}
/* صورة كورسات التأسيس بنسبة فيديو 16:9 */
.foundation-course-img {
  position: relative;
  z-index: 1;
  margin-bottom: 18px;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 4px 18px rgba(72, 206, 29, 0.10);
  background: #232323;
  width: 100%;
  aspect-ratio: 16/9;
  max-width: 320px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.foundation-course-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 14px;
  background: transparent;
}
.foundation-course-rating {
  margin: 12px 0 8px 0;
  font-size: 1.1rem;
  z-index: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}
.foundation-course-btn {
  margin: 20px auto 0 auto;
  display: block;
  font-size: 1.13rem;
  font-family: 'Tajawal', Arial, sans-serif;
  font-weight: 400;
  padding: 10px 0 9px 0;
  border-radius: 16px;
  background: transparent;
  color: #48ce1d;
  border: 2px solid #48ce1d;
  width: 96%;
  max-width: 260px;
  min-width: 90px;
  box-shadow: 0 2px 8px rgba(72, 206, 29, 0.07);
  cursor: pointer;
  letter-spacing: 1px;
  transition: background 0.22s, color 0.22s, box-shadow 0.22s, border-color 0.18s, transform 0.18s;
  position: relative;
  z-index: 1;
}
.foundation-course-btn:hover, .foundation-course-btn:focus {
  background: linear-gradient(90deg, #2ecc00 60%, #48ce1d 100%);
  color: #fff;
  border-color: #3eff00;
  box-shadow: 0 6px 18px rgba(72, 206, 29, 0.18);
  transform: translateY(-3px) scale(1.04);
}
.foundation-live-badge {
  position: absolute;
  bottom: 8px;
  left: 10px;
  right: auto;
  background: rgba(24, 24, 24, 0.55);
  color: #48ce1d;
  font-family: 'Cairo', Arial, sans-serif;
  font-size: 0.78rem;
  font-weight: 900;
  padding: 2px 10px 1px 10px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(72, 206, 29, 0.18), 0 1.5px 6px rgba(0,0,0,0.18);
  z-index: 2;
  letter-spacing: 1.2px;
  border: 1.5px solid rgba(72, 206, 29, 0.25);
  user-select: none;
  pointer-events: none;
  backdrop-filter: blur(2.5px);
  text-shadow: 0 1px 4px #191717, 0 0.5px 1px #fff2;
  transition: background 0.2s, color 0.2s;
}
@media (max-width: 700px) {
  .foundation-live-badge {
    font-size: 0.68rem;
    padding: 1.5px 7px 1px 7px;
    bottom: 4px;
    left: 5px;
    border-radius: 8px;
  }
}

/* زر الصعود للأعلى */
.back-to-top {
  position: fixed;
  right: 24px;
  left: auto;
  bottom: 24px;
  z-index: 3000;
  background: #191717;
  border: none;
  outline: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(72, 206, 29, 0.13);
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s, background 0.2s;
}
.back-to-top svg {
  display: block;
}
.back-to-top:hover {
  background: #232323;
}
.back-to-top.show {
  opacity: 1;
  pointer-events: auto;
}
@media (max-width: 700px) {
  .back-to-top {
    right: 12px;
    left: auto;
    bottom: 12px;
    width: 28px;
    height: 28px;
  }
}

.foundation-live-dot {
  display: inline-block;
  width: 9px;
  height: 9px;
  background: #ff2d2d;
  border-radius: 50%;
  margin-left: 6px;
  margin-bottom: 1.5px;
  box-shadow: 0 0 6px 1.5px #ff2d2d99;
  vertical-align: middle;
  border: 1.5px solid #fff;
  animation: live-dot-blink 1.2s infinite alternate;
}
@keyframes live-dot-blink {
  0% { opacity: 1; }
  100% { opacity: 0.5; }
}
@media (max-width: 700px) {
  .foundation-live-dot {
    width: 7px;
    height: 7px;
    margin-left: 4px;
  }
}

.foundation-course-name {
  display: inline-block;
  border: 2px solid #48ce1d;
  border-radius: 16px;
  padding: 6px 22px 5px 22px;
  background: transparent;
  font-size: 1.35rem !important;
  font-weight: 900 !important;
  letter-spacing: 1.2px !important;
  margin: 18px 0 12px 0 !important;
  text-shadow: 0 2px 8px rgba(0,0,0,0.13);
  line-height: 1.2;
  font-family: 'Cairo', Arial, sans-serif !important;
  box-shadow: 0 2px 8px rgba(72, 206, 29, 0.07);
  transition: border-color 0.2s, box-shadow 0.2s;
}
.foundation-course-name:hover, .foundation-course-name:focus {
  border-color: #3eff00;
  box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
}

.foundation-course-details {
  color: #cfcfcf;
  font-size: 1.01rem;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif;
  font-weight: 400;
  line-height: 1.7;
  margin: 0 0 14px 0;
  padding: 7px 10px 7px 10px;
  min-height: 0;
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  word-break: break-word;
  background: rgba(36,36,36,0.22);
  border-radius: 0;
  box-shadow: 0 1.5px 6px rgba(72, 206, 29, 0.07);
  text-shadow: 0 1px 4px #19171733;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: auto;
  transition: box-shadow 0.2s;
}
.foundation-course-details:hover, .foundation-course-details:focus {
  box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
}

.foundation-rating-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: rgba(36,36,36,0.22);
  border: 1.5px solid #48ce1d;
  border-radius: 12px;
  box-shadow: 0 1.5px 6px rgba(72, 206, 29, 0.07);
  padding: 5px 18px 4px 18px;
  margin: 12px auto 10px auto;
  width: fit-content;
  min-width: 120px;
  font-family: 'Cairo', Arial, sans-serif;
}
.foundation-rating-type {
  color: #fff;
  font-size: 0.98rem;
  font-weight: 700;
  background: rgba(72, 206, 29, 0.13);
  border-radius: 8px;
  padding: 2px 10px 1px 10px;
  margin-left: 2px;
  letter-spacing: 0.5px;
}
.foundation-rating-value {
  color: #48ce1d;
  font-size: 1.13rem;
  font-weight: 900;
  margin-left: 2px;
  margin-right: 2px;
}
.foundation-rating-stars {
  color: #ffd700;
  font-size: 1.13rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}
.foundation-rating-stars .star {
  margin: 0 0.5px;
  font-size: 1.13rem;
  color: #ffd700;
  filter: drop-shadow(0 1px 2px #19171733);
}
@media (max-width: 700px) {
  .foundation-rating-box {
    padding: 3px 8px 2px 8px;
    border-radius: 7px;
    gap: 6px;
    min-width: 0;
  }
  .foundation-rating-type {
    font-size: 0.81rem;
    padding: 1.5px 6px 1px 6px;
    border-radius: 5px;
  }
  .foundation-rating-value, .foundation-rating-stars {
    font-size: 0.93rem;
  }
}

.achievements-section {
  background: #181818;
  padding: 54px 0 54px 0;
  margin: 0;
  text-align: center;
}
.achievements-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
}
.achievements-title {
  color: #48ce1d;
  font-size: 2.1rem;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 800;
  margin-bottom: 38px;
  letter-spacing: 0.5px;
  text-align: center;
}
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 32px;
  width: 100%;
  justify-items: center;
}
.achievement-card {
  background: #202020;
  border-radius: 18px;
  padding: 0;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0,0,0,0.09);
  border: 1.5px solid #232323;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 220px;
  height: 170px;
  margin: 0 auto;
  overflow: hidden;
}
.achievement-number {
  margin-bottom: 10px;
  margin-top: 0;
}
@media (max-width: 700px) {
  .achievement-card {
    width: 100%;
    max-width: 100%;
    height: 110px;
    min-width: 0;
    min-height: 0;
    padding: 0;
    border-radius: 10px;
  }
}

.faq-section {
  background: #181818;
  padding: 54px 0 54px 0;
  margin: 0;
  text-align: center;
}
.faq-container {
  max-width: 700px;
  margin: 0 auto;
  padding: 0 20px;
}
.faq-title {
  color: #48ce1d;
  font-size: 2.1rem;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 800;
  margin-bottom: 18px;
  letter-spacing: 0.5px;
  text-align: center;
}
.faq-desc {
  color: #fff;
  font-size: 1.13rem;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif;
  font-weight: 400;
  margin-bottom: 38px;
  text-align: center;
}
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.faq-item {
  background: #202020;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.09);
  border: 1.5px solid #232323;
  overflow: hidden;
  text-align: right;
  transition: box-shadow 0.2s;
}
.faq-question {
  width: 100%;
  background: none;
  border: none;
  color: #fff;
  font-family: 'Cairo', Arial, sans-serif;
  font-size: 1.13rem;
  font-weight: 700;
  padding: 18px 24px;
  text-align: right;
  cursor: pointer;
  outline: none;
  transition: background 0.2s, color 0.2s;
  position: relative;
}
.faq-question::after {
  content: '+';
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.3rem;
  color: #48ce1d;
  transition: transform 0.2s;
}
.faq-item.active .faq-question::after {
  content: '-';
  transform: translateY(-50%) rotate(180deg);
}
.faq-answer {
  color: #fff;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif;
  font-size: 1.01rem;
  font-weight: 400;
  padding: 0 24px 18px 24px;
  display: none;
  background: #232323;
  border-top: 1px solid #48ce1d33;
  animation: fadeInFaq 0.3s;
}
.faq-item.active .faq-answer {
  display: block;
}
@keyframes fadeInFaq {
  from { opacity: 0; transform: translateY(10px);}
  to { opacity: 1; transform: translateY(0);}
}
@media (max-width: 700px) {
  .faq-section {
    padding: 20px 0 20px 0;
  }
  .faq-container {
    width: 95%;
    max-width: 340px;
    margin: 0 auto;
    padding: 0 0;
  }
  .faq-title {
    font-size: 1.1rem;
    margin-bottom: 8px;
  }
  .faq-desc {
    font-size: 0.91rem;
    margin-bottom: 10px;
  }
  .faq-list {
    gap: 8px;
  }
  .faq-item {
    border-radius: 6px;
  }
  .faq-question {
    font-size: 0.93rem;
    padding: 10px 8px;
  }
  .faq-answer {
    font-size: 0.85rem;
    padding: 0 8px 8px 8px;
  }
  .faq-question::after {
    font-size: 1.6rem;
    font-weight: bold;
    left: 10px;
    color: #48ce1d;
  }
  .faq-item.active .faq-question::after {
    color: #fff;
  }
}

.cta-success-section {
  background: linear-gradient(90deg, #48ce1d 0%, #3eff00 100%);
  padding: 54px 0 54px 0;
  text-align: center;
  margin: 0;
}
.cta-success-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
}
.cta-success-title {
  color: #191717;
  font-size: 2.1rem;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 900;
  margin-bottom: 18px;
  letter-spacing: 1px;
}
.cta-success-desc {
  color: #191717;
  font-size: 1.13rem;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif;
  font-weight: 400;
  margin-bottom: 32px;
}
.cta-success-btn {
  display: inline-block;
  background: #191717;
  color: #48ce1d;
  font-family: 'Cairo', Arial, sans-serif;
  font-size: 1.15rem;
  font-weight: 800;
  padding: 14px 38px;
  border-radius: 24px;
  text-decoration: none;
  box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
  transition: background 0.2s, color 0.2s, transform 0.18s;
  border: 2px solid #191717;
  letter-spacing: 1px;
}
.cta-success-btn:hover, .cta-success-btn:focus {
  background: #fff;
  color: #191717;
  border-color: #48ce1d;
  transform: translateY(-2px) scale(1.04);
}
@media (max-width: 700px) {
  .cta-success-section {
    padding: 32px 0 32px 0;
  }
  .cta-success-title {
    font-size: 1.3rem;
    margin-bottom: 10px;
  }
  .cta-success-desc {
    font-size: 0.98rem;
    margin-bottom: 18px;
  }
  .cta-success-btn {
    font-size: 1rem;
    padding: 10px 18px;
    border-radius: 16px;
  }
}

.cta-community-section {
  background: linear-gradient(90deg, #48ce1d 0%, #3eff00 100%);
  padding: 54px 0 54px 0;
  text-align: center;
  margin: 0;
}
@media (max-width: 700px) {
  .cta-community-section {
    padding: 32px 0 32px 0;
  }
}

.virtual-school-section {
  background: #181818;
  padding: 54px 0 54px 0;
  margin: 0;
}
.virtual-school-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
  flex-wrap: wrap;
}
.virtual-school-image {
  flex: 1 1 340px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.virtual-school-image img {
  width: 480px;
  max-width: 100%;
  border-radius: 22px;
  box-shadow: none;
  background: none;
  border: none;
}
@media (max-width: 900px) {
  .virtual-school-image img {
    width: 99vw;
    max-width: 99vw;
    border-radius: 16px;
    box-shadow: none;
    background: none;
    border: none;
  }
}
@media (max-width: 700px) {
  .virtual-school-image {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .virtual-school-image img {
    display: block !important;
    width: 100vw;
    max-width: 100vw;
    height: auto;
    margin: 0 auto 0 auto !important;
    border-radius: 16px;
    box-shadow: none;
    background: none;
    border: none;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    transform: scale(1.5);
    transition: transform 0.2s;
  }
  .virtual-school-content {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  .virtual-school-title, .virtual-school-desc {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
}
.virtual-school-content {
  flex: 1 1 340px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  text-align: right;
  min-width: 260px;
}
.virtual-school-title {
  color: #48ce1d;
  font-size: 2.1rem;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 900;
  margin-bottom: 18px;
  letter-spacing: 1px;
}
.virtual-school-desc {
  color: #fff;
  font-size: 1.13rem;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif;
  font-weight: 400;
  margin-bottom: 32px;
}
.virtual-school-btn {
  display: inline-block;
  background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%);
  color: #191717;
  font-family: 'Cairo', Arial, sans-serif;
  font-size: 1.15rem;
  font-weight: 800;
  padding: 14px 38px;
  border-radius: 24px;
  text-decoration: none;
  box-shadow: 0 4px 16px rgba(72, 206, 29, 0.13);
  transition: background 0.2s, color 0.2s, transform 0.18s;
  border: none;
  letter-spacing: 1px;
}
.virtual-school-btn:hover, .virtual-school-btn:focus {
  background: #fff;
  color: #191717;
  transform: translateY(-2px) scale(1.04);
}
@media (max-width: 900px) {
  .virtual-school-container {
    flex-direction: column;
    gap: 24px;
    align-items: center;
    text-align: center;
  }
  .virtual-school-content {
    align-items: center;
    text-align: center;
  }
  .virtual-school-image img {
    width: 95vw;
    max-width: 95vw;
    border-radius: 16px;
  }
}
@media (max-width: 700px) {
  .virtual-school-section {
    padding: 32px 0 32px 0;
  }
  .virtual-school-title {
    font-size: 1.3rem;
    margin-bottom: 10px;
  }
  .virtual-school-desc {
    font-size: 0.98rem;
    margin-bottom: 18px;
  }
  .virtual-school-btn {
    font-family: 'Cairo', Arial, sans-serif;
    font-size: 1.1rem;
    font-weight: 300;
    padding: 10px 0;
    border-radius: 8px;
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
    width: 100%;
    max-width: 220px;
    min-width: 0;
    margin: 18px auto 18px auto;
    display: block;
    text-align: center;
    box-shadow: none !important;
    transform: none !important;
    transition: background 0.2s, color 0.2s, border-color 0.2s;
  }
  .virtual-school-btn:hover, .virtual-school-btn:focus {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-color: #3eff00;
    box-shadow: none !important;
    transform: none !important;
  }
  .virtual-school-image img {
    display: block !important;
    width: 95vw;
    max-width: 95vw;
    height: auto;
    margin: 0 auto 18px auto;
    border-radius: 16px;
  }
}

/* تحسين تصميم الـ Footer */
.main-footer {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
  color: #fff;
  padding: 0;
  font-family: 'Cairo', Arial, sans-serif;
  border-top: 3px solid transparent;
  background-clip: padding-box;
  position: relative;
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
  overflow: hidden;
}

/* إضافة تأثير الضوء في الأعلى */
.main-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #48ce1d 0%, #3eff00 50%, #48ce1d 100%);
  box-shadow: 0 0 20px rgba(72, 206, 29, 0.5);
  animation: footerGlow 3s ease-in-out infinite alternate;
}

@keyframes footerGlow {
  0% { box-shadow: 0 0 20px rgba(72, 206, 29, 0.3); }
  100% { box-shadow: 0 0 30px rgba(72, 206, 29, 0.7); }
}

/* إضافة تأثيرات الخلفية المتحركة */
.main-footer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(72, 206, 29, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(62, 255, 0, 0.05) 0%, transparent 50%);
  pointer-events: none;
  animation: backgroundFloat 8s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
}

.footer-top {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 40px;
  padding: 60px 0 30px 0;
  align-items: start;
}

/* تحسين قسم العلامة التجارية */
.footer-brand {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}

.footer-logo {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(72, 206, 29, 0.2) 0%, rgba(62, 255, 0, 0.1) 100%);
  box-shadow: 
    0 8px 32px rgba(72, 206, 29, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  object-fit: cover;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(72, 206, 29, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.footer-logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(72, 206, 29, 0.1), transparent);
  transform: rotate(45deg);
  transition: transform 0.6s;
  opacity: 0;
}

.footer-logo:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 
    0 12px 40px rgba(72, 206, 29, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(72, 206, 29, 0.6);
}

.footer-logo:hover::before {
  transform: rotate(45deg) translateX(100%);
  opacity: 1;
}

.footer-brand-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-brand-title {
  color: #48ce1d;
  font-size: 1.8rem;
  font-weight: 900;
  letter-spacing: 1px;
  text-shadow: 0 2px 10px rgba(72, 206, 29, 0.3);
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.footer-brand-title:hover {
  transform: scale(1.05);
  text-shadow: 0 4px 15px rgba(72, 206, 29, 0.5);
}

.footer-brand-desc {
  color: #fff;
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
  font-weight: 400;
}

/* تحسين الروابط */
.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  justify-content: center;
}

.footer-links h4 {
  color: #48ce1d;
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 700;
  position: relative;
  padding-bottom: 10px;
}

.footer-links h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 3px;
  background: linear-gradient(90deg, #48ce1d 0%, #3eff00 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.footer-links h4:hover::after {
  width: 50px;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: 12px;
  position: relative;
  transition: all 0.3s ease;
  transform: translateX(0);
}

.footer-links ul li:hover {
  transform: translateX(-8px);
}

.footer-links ul li a {
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  padding: 5px 0;
  display: inline-block;
  border-radius: 6px;
  padding: 8px 12px;
  margin: -8px -12px;
}

.footer-links ul li a:hover {
  color: #48ce1d;
  background: rgba(72, 206, 29, 0.1);
  box-shadow: 0 2px 8px rgba(72, 206, 29, 0.2);
}

.footer-links ul li::before {
  content: '▶';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: #48ce1d;
  font-size: 0.7rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-links ul li:hover::before {
  opacity: 1;
  right: -20px;
}

.footer-links ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #48ce1d 0%, #3eff00 100%);
  transition: width 0.3s ease;
}

.footer-links ul li a:hover {
  color: #48ce1d;
  transform: translateX(-5px);
}

.footer-links ul li a:hover::after {
  width: 100%;
}

/* تحسين التواصل الاجتماعي */
.footer-social {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.footer-social h4 {
  color: #48ce1d;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 700;
  text-align: center;
}

.social-icons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.social-icon-modern {
  width: 50px;
  height: 50px;
  border-radius: 15px;
  background: linear-gradient(135deg, rgba(72, 206, 29, 0.1) 0%, rgba(62, 255, 0, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(72, 206, 29, 0.2);
}

.social-icon-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(72, 206, 29, 0.2), transparent);
  transition: left 0.5s ease;
}

.social-icon-modern:hover::before {
  left: 100%;
}

.social-icon-modern:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 
    0 10px 30px rgba(72, 206, 29, 0.3),
    0 5px 15px rgba(0, 0, 0, 0.3);
  border-color: rgba(72, 206, 29, 0.5);
}

.social-icon-modern img {
  width: 28px;
  height: 28px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.social-icon-modern:hover img {
  transform: scale(1.2);
}

/* إضافة قسم النشرة الإخبارية */
.newsletter-section {
  background: linear-gradient(135deg, rgba(72, 206, 29, 0.1) 0%, rgba(62, 255, 0, 0.05) 100%);
  border-radius: 20px;
  padding: 30px;
  margin: 20px 0;
  border: 2px solid rgba(72, 206, 29, 0.2);
  position: relative;
  overflow: hidden;
}

.newsletter-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(72, 206, 29, 0.1) 0%, transparent 70%);
  animation: newsletterGlow 4s ease-in-out infinite;
}

@keyframes newsletterGlow {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.newsletter-content {
  position: relative;
  z-index: 2;
}

.newsletter-title {
  color: #48ce1d;
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-align: center;
}

.newsletter-desc {
  color: #fff;
  font-size: 0.95rem;
  opacity: 0.9;
  text-align: center;
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
  gap: 10px;
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid rgba(72, 206, 29, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.newsletter-input:focus {
  outline: none;
  border-color: #48ce1d;
  box-shadow: 0 0 20px rgba(72, 206, 29, 0.3);
  background: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

.newsletter-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
  border: none;
  border-radius: 12px;
  color: #000;
  font-weight: 700;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(72, 206, 29, 0.3);
  position: relative;
  overflow: hidden;
}

.newsletter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 206, 29, 0.4);
}

.newsletter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.newsletter-btn:hover::before {
  left: 100%;
}

/* تحسين القسم السفلي */
.footer-bottom {
  border-top: 2px solid rgba(72, 206, 29, 0.2);
  padding: 25px 0 20px 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  font-size: 0.95rem;
  color: #cfcfcf;
  opacity: 0.9;
  gap: 15px;
  position: relative;
}

.footer-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #48ce1d, transparent);
}

.footer-bottom span:first-child {
  color: #48ce1d;
  font-weight: 700;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(72, 206, 29, 0.3);
}

.footer-bottom span:last-child {
  color: #fff;
  font-weight: 600;
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 1px;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: rgba(72, 206, 29, 0.1);
  border: 1px solid rgba(72, 206, 29, 0.3);
  transition: all 0.3s ease;
}

.footer-bottom span:last-child:hover {
  background-color: rgba(72, 206, 29, 0.2);
  border-color: rgba(72, 206, 29, 0.5);
  transform: translateY(-2px);
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 900px) {
  .footer-top {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  
  .footer-brand {
    align-items: center;
  }
  
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .footer-social {
    order: -1;
  }
  
  .newsletter-form {
    flex-direction: column;
    max-width: 300px;
  }
}

@media (max-width: 700px) {
  .footer-container {
    padding: 0 16px;
  }
  
  .footer-top {
    padding: 40px 0 20px 0;
    gap: 25px;
  }
  
  .footer-logo {
    width: 60px;
    height: 60px;
  }
  
  .footer-brand-title {
    font-size: 1.5rem;
  }
  
  .footer-brand-desc {
    font-size: 1rem;
  }
  
  .footer-links {
    grid-template-columns: 1fr;
    gap: 25px;
    text-align: center;
  }
  
  .footer-links h4 {
    font-size: 1.1rem;
    margin-bottom: 15px;
  }
  
  .footer-links ul li a {
    font-size: 0.95rem;
  }
  
  .social-icons-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .social-icon-modern {
    width: 45px;
    height: 45px;
  }
  
  .social-icon-modern img {
    width: 24px;
    height: 24px;
  }
  
  .newsletter-section {
    padding: 20px;
    margin: 15px 0;
  }
  
  .newsletter-title {
    font-size: 1.1rem;
  }
  
  .newsletter-desc {
    font-size: 0.9rem;
  }
  
  .footer-bottom {
    font-size: 0.85rem;
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 20px 0 15px 0;
  }
}

/* إضافة تأثيرات التمرير */
.footer-top {
  opacity: 0;
  transform: translateY(30px);
  animation: footerSlideIn 0.8s ease-out forwards;
}

@keyframes footerSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.footer-links {
  animation: footerLinksFadeIn 1s ease-out 0.2s forwards;
  opacity: 0;
}

@keyframes footerLinksFadeIn {
  to {
    opacity: 1;
  }
}

.footer-social {
  animation: footerSocialFadeIn 1s ease-out 0.4s forwards;
  opacity: 0;
}

@keyframes footerSocialFadeIn {
  to {
    opacity: 1;
  }
}

/* إخفاء العناصر على الهاتف */
@media (max-width: 700px) {
  .hide-on-mobile {
    display: none !important;
  }
}

@media (max-width: 700px) {
  .courses-main {
    padding-top: 18px !important;
  }
  .courses-hero-section {
    margin-top: 0 !important;
    margin-bottom: 2px !important;
  }
  .search-bar-container {
    margin-bottom: 2px !important;
  }
  .foundation-courses-section {
    margin-top: 2px !important;
  }
  .hero-img-col {
    display: none !important;
  }
  input[type="text"] {
    border-radius: 4px !important;
    padding: 10px 10px !important;
    font-size: 1rem !important;
  }
  .search-bar-container {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 8px !important;
  }
  .search-bar-container > div {
    flex-direction: row !important;
    display: flex !important;
    justify-content: center !important;
    gap: 8px !important;
  }
  .search-bar-container button {
    padding: 10px 14px !important;
    font-size: 1rem !important;
    min-width: 0 !important;
    white-space: nowrap !important;
  }
}

@media (min-width: 701px) {
  .login-container {
    max-width: 480px !important;
    padding: 50px !important;
    border-radius: 28px !important;
  }
  
  .login-container h1 {
    font-size: 2.2rem !important;
    margin-bottom: 12px !important;
  }
  
  .login-container p {
    font-size: 1.05rem !important;
  }
  
  .login-container input[type="email"],
  .login-container input[type="password"] {
    padding: 18px 24px !important;
    font-size: 1.1rem !important;
    border-radius: 14px !important;
  }
  
  .login-container button[type="submit"] {
    padding: 18px 32px !important;
    font-size: 1.2rem !important;
    border-radius: 14px !important;
  }
  
  .login-container label {
    font-size: 1rem !important;
  }
  
  .login-container img[alt="شعار دارين"] {
    width: 100px !important;
    height: 100px !important;
  }
}

@media (max-width: 700px) {
  .login-split-container {
    flex-direction: column !important;
    max-width: 420px !important;
  }
  
  .login-image-section {
    display: none !important;
  }
  
  .login-form-section {
    padding: 40px 30px !important;
  }
  
  .login-form-section h1 {
    font-size: 1.8rem !important;
  }
  
  .login-form-section p {
    font-size: 0.95rem !important;
  }
  
  .login-form-section input[type="email"],
  .login-form-section input[type="password"] {
    padding: 16px 20px !important;
    font-size: 1rem !important;
  }
  
  .login-form-section button[type="submit"] {
    padding: 16px 24px !important;
    font-size: 1.1rem !important;
  }
}

@media (min-width: 701px) {
  .login-split-container {
    max-width: 900px !important;
  }
  
  .login-image-section {
    min-height: 350px !important;
    padding: 40px 30px !important;
  }
  
  .login-form-section {
    padding: 40px 35px !important;
  }
  
  .login-form-section h1 {
    font-size: 2rem !important;
    margin-bottom: 6px !important;
  }
  
  .login-form-section p {
    font-size: 0.95rem !important;
  }
  
  .login-form-section input[type="email"],
  .login-form-section input[type="password"] {
    padding: 16px 18px !important;
    font-size: 1rem !important;
  }
  
  .login-form-section button[type="submit"] {
    padding: 16px 22px !important;
    font-size: 1.1rem !important;
  }
  
  .login-form-section > div:first-child {
    margin-bottom: 24px !important;
  }
}

/* قسم وفر أكثر مع باقات علا */
.uula-packages-section {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
  padding: 80px 0;
  position: relative;
  overflow: hidden;
  margin: 0;
  border-radius: 0 0 50px 50px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.uula-packages-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(72, 206, 29, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(62, 255, 0, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(72, 206, 29, 0.05) 0%, transparent 70%);
  pointer-events: none;
  animation: newsletterBackgroundFloat 15s ease-in-out infinite;
}

.uula-packages-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 30%, rgba(72, 206, 29, 0.03) 50%, transparent 70%);
  animation: newsletterShimmer 8s ease-in-out infinite;
}

@keyframes newsletterBackgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  33% { transform: translateY(-20px) rotate(1deg) scale(1.02); }
  66% { transform: translateY(10px) rotate(-1deg) scale(0.98); }
}

@keyframes newsletterShimmer {
  0%, 100% { opacity: 0; transform: translateX(-100%); }
  50% { opacity: 1; transform: translateX(100%); }
}

.uula-packages-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 2;
}

.uula-packages-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.uula-packages-text {
  color: #fff;
  position: relative;
}

.uula-packages-text::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #48ce1d, #3eff00);
  border-radius: 50%;
  opacity: 0.1;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.1; }
  50% { transform: scale(1.2); opacity: 0.2; }
}

.uula-packages-title {
  font-size: 3.2rem;
  font-weight: 900;
  font-family: 'Cairo', Arial, sans-serif;
  margin-bottom: 25px;
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 50%, #48ce1d 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 6px 20px rgba(72, 206, 29, 0.4);
  line-height: 1.1;
  position: relative;
  animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.1); }
}

.uula-packages-desc {
  font-size: 1.3rem;
  color: #fff;
  opacity: 0.9;
  margin-bottom: 35px;
  line-height: 1.7;
  font-weight: 500;
  font-family: 'Cairo', Arial, sans-serif;
}

.uula-packages-features {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 30px;
}

.uula-feature {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  color: #fff;
  opacity: 0.9;
  transition: all 0.4s ease;
  padding: 12px 16px;
  border-radius: 12px;
  background: rgba(72, 206, 29, 0.08);
  border: 1px solid rgba(72, 206, 29, 0.15);
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 500;
}

.uula-feature:hover {
  opacity: 1;
  transform: translateX(8px);
  background: rgba(72, 206, 29, 0.15);
  border-color: rgba(72, 206, 29, 0.3);
  box-shadow: 0 4px 15px rgba(72, 206, 29, 0.2);
}

.uula-feature-icon {
  font-size: 1.3rem;
  filter: drop-shadow(0 2px 4px rgba(72, 206, 29, 0.4));
  animation: iconFloat 2s ease-in-out infinite;
}

.newsletter-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #fff;
  opacity: 0.9;
  transition: all 0.4s ease;
  padding: 10px 16px;
  border-radius: 12px;
  background: rgba(72, 206, 29, 0.08);
  border: 1px solid rgba(72, 206, 29, 0.15);
  font-family: 'Cairo', Arial, sans-serif;
  cursor: pointer;
  min-width: fit-content;
  white-space: nowrap;
}

.newsletter-feature:hover {
  opacity: 1;
  transform: translateY(-3px);
  background: rgba(72, 206, 29, 0.15);
  border-color: rgba(72, 206, 29, 0.4);
  box-shadow: 0 6px 20px rgba(72, 206, 29, 0.3);
}

.newsletter-feature-icon {
  font-size: 1.1rem;
  filter: drop-shadow(0 2px 4px rgba(72, 206, 29, 0.4));
  animation: iconFloat 2s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

.uula-packages-form {
  background: linear-gradient(135deg, rgba(72, 206, 29, 0.2) 0%, rgba(62, 255, 0, 0.1) 100%);
  border-radius: 30px;
  padding: 45px;
  border: 3px solid rgba(72, 206, 29, 0.4);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(72, 206, 29, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.15);
}

.uula-packages-form::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(72, 206, 29, 0.15) 0%, transparent 70%);
  animation: newsletterFormGlow 8s ease-in-out infinite;
}

.uula-packages-form::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.05) 50%, transparent 60%);
  animation: formShine 4s ease-in-out infinite;
}

@keyframes newsletterFormGlow {
  0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.5; }
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.8; }
}

@keyframes formShine {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.uula-packages-form-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.uula-savings-highlight {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(72, 206, 29, 0.15) 0%, rgba(62, 255, 0, 0.1) 100%);
  border-radius: 20px;
  border: 2px solid rgba(72, 206, 29, 0.3);
}

.uula-savings-text {
  display: block;
  font-size: 1.1rem;
  color: #fff;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 500;
  margin-bottom: 10px;
}

.uula-savings-percentage {
  display: block;
  font-size: 3rem;
  font-weight: 900;
  color: #48ce1d;
  font-family: 'Cairo', Arial, sans-serif;
  text-shadow: 0 4px 15px rgba(72, 206, 29, 0.4);
  animation: titleGlow 3s ease-in-out infinite;
}

.uula-packages-list {
  margin-bottom: 30px;
}

.uula-package-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(72, 206, 29, 0.2);
  transition: all 0.3s ease;
}

.uula-package-item:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(72, 206, 29, 0.4);
  transform: translateX(5px);
}

.uula-package-icon {
  font-size: 1.2rem;
  color: #48ce1d;
  filter: drop-shadow(0 2px 4px rgba(72, 206, 29, 0.3));
}

.uula-package-text {
  font-size: 1rem;
  color: #fff;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 500;
}

.uula-explore-btn {
  padding: 18px 32px;
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 50%, #48ce1d 100%);
  border: none;
  border-radius: 20px;
  color: #000;
  font-weight: 800;
  font-size: 1rem;
  font-family: 'Cairo', Arial, sans-serif;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 
    0 8px 25px rgba(72, 206, 29, 0.5),
    0 4px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  min-width: 200px;
  min-height: 60px;
  justify-content: center;
  margin: 0 auto;
}

.uula-explore-btn:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 12px 35px rgba(72, 206, 29, 0.6),
    0 6px 20px rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, #3eff00 0%, #48ce1d 50%, #3eff00 100%);
}

.uula-explore-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.uula-explore-btn:hover::before {
  left: 100%;
}

.uula-explore-btn svg {
  transition: transform 0.4s ease;
  width: 20px;
  height: 20px;
}

.uula-explore-btn:hover svg {
  transform: translateX(5px) scale(1.1);
}

.newsletter-input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  position: relative;
}

.newsletter-country-select {
  padding: 18px 16px;
  border: 2px solid rgba(72, 206, 29, 0.5);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-size: 1rem;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 600;
  transition: all 0.4s ease;
  backdrop-filter: blur(20px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  min-width: 170px;
  max-width: 190px;
}

.newsletter-country-select:focus {
  outline: none;
  border-color: #48ce1d;
  box-shadow: 
    0 0 30px rgba(72, 206, 29, 0.5),
    0 8px 25px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.18);
  transform: scale(1.02);
}

.newsletter-country-select option {
  background: #1a1a1a;
  color: #fff;
  font-family: 'Cairo', Arial, sans-serif;
}

.newsletter-hero-input {
  flex: 1;
  padding: 18px 22px;
  border: 2px solid rgba(72, 206, 29, 0.5);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-size: 1rem;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: 600;
  transition: all 0.4s ease;
  backdrop-filter: blur(20px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  min-height: 60px;
}

.newsletter-hero-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.newsletter-hero-input:focus {
  outline: none;
  border-color: #48ce1d;
  box-shadow: 
    0 0 30px rgba(72, 206, 29, 0.5),
    0 8px 25px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.18);
  transform: scale(1.02);
}

.newsletter-hero-btn {
  padding: 18px 32px;
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 50%, #48ce1d 100%);
  border: none;
  border-radius: 20px;
  color: #000;
  font-weight: 800;
  font-size: 1rem;
  font-family: 'Cairo', Arial, sans-serif;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 
    0 8px 25px rgba(72, 206, 29, 0.5),
    0 4px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  min-width: 150px;
  min-height: 60px;
  justify-content: center;
}

.newsletter-hero-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.newsletter-hero-btn:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 
    0 12px 35px rgba(72, 206, 29, 0.5),
    0 6px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #3eff00 0%, #48ce1d 50%, #3eff00 100%);
}

.newsletter-hero-btn:hover::before {
  left: 100%;
}

.newsletter-hero-btn svg {
  transition: transform 0.4s ease;
  width: 20px;
  height: 20px;
}

.newsletter-hero-btn:hover svg {
  transform: translateX(5px) scale(1.1);
}

.newsletter-hero-note {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
  font-family: 'Cairo', Arial, sans-serif;
  padding: 12px;
  background: rgba(72, 206, 29, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(72, 206, 29, 0.1);
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 900px) {
  .newsletter-hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .newsletter-hero-features {
    margin-top: 15px;
  }
  
  .uula-packages-title {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
  
  .uula-packages-desc {
    font-size: 1.1rem;
    margin-bottom: 25px;
  }
  
  .newsletter-input-group {
    flex-direction: column;
    gap: 15px;
  }
  
  .newsletter-country-select {
    min-width: 100%;
    padding: 14px 12px;
    font-size: 0.9rem;
    border-radius: 16px;
  }
  
  .newsletter-hero-btn {
    width: 100%;
    justify-content: center;
    padding: 16px 24px;
  }
  
  .newsletter-hero-form {
    max-width: 450px;
    margin: 0 auto;
  }
}

@media (max-width: 700px) {
  .newsletter-hero-section {
    padding: 60px 0;
    border-radius: 0 0 25px 25px;
  }
  
  .newsletter-hero-container {
    padding: 0 18px;
  }
  
  .newsletter-hero-features {
    margin-top: 15px;
  }
  
  .newsletter-hero-form {
    padding: 28px 20px;
    border-radius: 20px;
  }
  
  .newsletter-hero-input {
    padding: 14px 18px;
    font-size: 0.9rem;
    border-radius: 16px;
    min-height: 52px;
  }
  
  .newsletter-country-select {
    padding: 14px 12px;
    font-size: 0.9rem;
    border-radius: 16px;
    min-width: 100%;
    min-height: 52px;
  }
  
  .newsletter-hero-btn {
    padding: 14px 24px;
    font-size: 0.9rem;
    border-radius: 16px;
    min-height: 52px;
  }
  
  .newsletter-hero-note {
    font-size: 0.8rem;
    padding: 10px;
  }
  
  .uula-packages-title {
    font-size: 2.2rem;
    margin-bottom: 20px;
  }
  
  .uula-packages-desc {
    font-size: 1rem;
    margin-bottom: 25px;
  }
}

@media (max-width: 480px) {
  .newsletter-hero-section {
    padding: 60px 0;
  }
  
  .newsletter-hero-container {
    padding: 0 16px;
  }
  
  .newsletter-hero-title {
    font-size: 1.9rem;
  }
  
  .newsletter-hero-desc {
    font-size: 1rem;
  }
  
  .newsletter-hero-form {
    padding: 30px 20px;
  }
  
  .newsletter-hero-input {
    padding: 14px 18px;
    font-size: 0.95rem;
  }
  
  .newsletter-hero-btn {
    padding: 14px 20px;
    font-size: 0.95rem;
  }
  
  .newsletter-hero-note {
    font-size: 0.85rem;
  }
}

/* إضافة تأثيرات التمرير والتفاعل */
.newsletter-hero-section {
  opacity: 0;
  transform: translateY(40px);
  animation: newsletterHeroSlideIn 1.2s ease-out 0.5s forwards;
}

@keyframes newsletterHeroSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.newsletter-hero-text {
  opacity: 0;
  transform: translateX(-30px);
  animation: newsletterTextSlideIn 1.2s ease-out 0.8s forwards;
}

@keyframes newsletterTextSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.newsletter-hero-form {
  opacity: 0;
  transform: translateX(30px);
  animation: newsletterFormSlideIn 1.2s ease-out 1s forwards;
}

@keyframes newsletterFormSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تحسينات إضافية للتفاعل */
.newsletter-hero-input:valid {
  border-color: #48ce1d;
  box-shadow: 0 0 20px rgba(72, 206, 29, 0.3);
}

.newsletter-hero-input:invalid:not(:placeholder-shown) {
  border-color: #ff4444;
  box-shadow: 0 0 20px rgba(255, 68, 68, 0.3);
}

.newsletter-hero-btn:active {
  transform: translateY(-2px) scale(0.98);
}

/* تأثير النجمة عند النقر على الزر */
.newsletter-hero-btn::after {
  content: '✨';
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 0.8rem;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.newsletter-hero-btn:hover::after {
  opacity: 1;
  transform: scale(1.2) rotate(15deg);
}

/* تحسين تأثيرات الأيقونات */
.newsletter-feature-icon {
  position: relative;
}

.newsletter-feature-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(72, 206, 29, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  opacity: 0;
}

.newsletter-feature:hover .newsletter-feature-icon::after {
  width: 40px;
  height: 40px;
  opacity: 1;
}

/* تأثير التحميل للزر */
.newsletter-hero-btn.loading {
  pointer-events: none;
  opacity: 0.8;
}

.newsletter-hero-btn.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* تأثير النجاح عند الإرسال */
.newsletter-hero-form.success {
  border-color: #48ce1d;
  box-shadow: 0 0 30px rgba(72, 206, 29, 0.5);
}

.newsletter-hero-form.success::before {
  content: '✓';
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 2rem;
  color: #48ce1d;
  animation: successCheck 0.5s ease-out;
}

@keyframes successCheck {
  0% { transform: scale(0) rotate(-45deg); }
  50% { transform: scale(1.2) rotate(0deg); }
  100% { transform: scale(1) rotate(0deg); }
}

/* تصميم لوحة تحكم المدير */
.admin-body {
    background: #f5f5f5;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', Arial, sans-serif;
    display: flex;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* الشريط الجانبي */
.admin-sidebar {
    width: 300px;
    background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    right: 0;
    top: 0;
    z-index: 1000;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 300px;
    border-radius: 0 0 0 20px;
    overflow: hidden;
}

.sidebar-header {
    padding: 40px 25px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.admin-avatar {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    margin: 0 auto 20px;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.25);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.admin-avatar:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.admin-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 18px 30px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 30px 0 0 30px;
    margin-left: 20px;
    margin-right: 10px;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.nav-link:hover::before {
    transform: translateX(0);
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-item.active .nav-link {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    border-right: 4px solid #48ce1d;
}

.nav-item.active .nav-link::before {
    transform: translateX(0);
}

.nav-icon {
    font-size: 1.3rem;
    margin-right: 18px;
    width: 28px;
    text-align: center;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.nav-text {
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-link:hover .nav-text {
    transform: translateX(3px);
}

.sidebar-footer {
    padding: 25px 20px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(5px);
}

.version-text {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 700;
    color: #48ce1d;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

/* المحتوى الرئيسي */
.admin-main {
    flex: 1;
    margin-left: 300px;
    background: #f8fafc;
    min-height: 100vh;
    width: calc(100% - 300px);
    position: relative;
    left: 0;
    right: 300px;
    max-width: calc(100% - 300px);
}

/* شريط الرأس */
.admin-header {
    background: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e2e8f0;
}

.header-left {
    display: flex;
    align-items: center;
}

.logout-btn {
    background: #ef4444;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.logout-icon {
    font-size: 1rem;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e2e8f0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    text-align: right;
}

.welcome-text {
    font-size: 0.85rem;
    color: #64748b;
    margin: 0;
}

.user-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* المحتوى الرئيسي */
.admin-content {
    padding: 30px;
}

/* قسم إضافة طالب */
.add-student-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f5f9;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.section-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn {
    background: #10b981;
    color: white;
}

.save-btn:hover {
    background: #059669;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.delete-btn {
    background: #ef4444;
    color: white;
}

.delete-btn:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* نموذج الطالب */
.student-form {
    margin-top: 20px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.form-input {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

textarea.form-input {
    resize: vertical;
    min-height: 80px;
}

/* مجموعة الجنسية */
.nationality-group {
    grid-column: span 2;
}

.checkbox-group {
    display: flex;
    gap: 20px;
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.95rem;
    color: #374151;
}

.checkbox-label input[type="radio"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="radio"]:checked + .checkmark {
    border-color: #3b82f6;
    background: #3b82f6;
}

.checkbox-label input[type="radio"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

/* قسم الطلاب المسجلين */
.registered-students-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.students-header {
    background: #f97316;
    padding: 20px 30px;
}

.students-title {
    color: white;
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0;
}

.students-list {
    padding: 0;
}

.student-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.student-card:hover {
    background: #f8fafc;
}

.student-card:last-child {
    border-bottom: none;
}

.student-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.student-avatar {
    width: 40px;
    height: 40px;
    background: #10b981;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.student-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.student-actions {
    display: flex;
    gap: 10px;
}

.edit-btn {
    background: #3b82f6;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.open-btn {
    background: #10b981;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.open-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* الترقيم */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 20px 30px;
    background: #f8fafc;
}

.pagination-btn {
    background: white;
    border: 1px solid #e2e8f0;
    color: #64748b;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 600;
}

.pagination-btn:hover {
    background: #f1f5f9;
    color: #374151;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-ellipsis {
    color: #64748b;
    font-size: 0.9rem;
    margin: 0 4px;
}

.pagination-number {
    background: white;
    border: 1px solid #e2e8f0;
    color: #64748b;
    width: 35px;
    height: 35px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-number:hover {
    background: #f1f5f9;
    color: #374151;
}

.pagination-number.active {
    background: #ef4444;
    color: white;
    border-color: #ef4444;
}

/* تحسينات للهواتف المحمولة */
@media (max-width: 1024px) {
    .admin-sidebar {
        width: 280px;
        min-width: 280px;
        max-width: 280px;
    }
    
    .admin-main {
        margin-left: 280px;
        width: calc(100% - 280px);
        max-width: calc(100% - 280px);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .nationality-group {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.active {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-header {
        padding: 15px 20px;
    }
    
    .admin-content {
        padding: 20px;
    }
    
    .add-student-section {
        padding: 20px;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .section-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .student-card {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .student-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
}

/* إضافة زر القائمة للهواتف */
.mobile-menu-toggle {
    display: none;
    background: #3b82f6;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
}


