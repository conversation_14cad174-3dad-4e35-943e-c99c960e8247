// Chat Widget JavaScript
class ChatWidget {
    constructor() {
        this.isOpen = false;
        this.messages = [];
        this.typingTimeout = null;
        this.init();
    }

    init() {
        this.createWidget();
        this.bindEvents();
        this.loadChatHistory();
        this.showWelcomeMessage();
    }

    createWidget() {
        const widgetHTML = `
            <div class="chat-widget">
                <div class="chat-widget-icon" id="chatWidgetIcon">
                    <span>🤖</span>
                </div>
                <div class="chat-widget-container" id="chatWidgetContainer">
                    <div class="chat-widget-header">
                        <h3>معهد دارين - المساعد الذكي</h3>
                        <button class="chat-widget-close" id="chatWidgetClose">×</button>
                    </div>
                    <div class="chat-widget-messages" id="chatWidgetMessages">
                        <!-- Messages will be added here -->
                    </div>
                    <div class="chat-widget-typing" id="chatWidgetTyping">
                        <div class="chat-widget-avatar">🤖</div>
                        <div class="chat-widget-typing-dots">
                            <div class="chat-widget-typing-dot"></div>
                            <div class="chat-widget-typing-dot"></div>
                            <div class="chat-widget-typing-dot"></div>
                        </div>
                    </div>
                    <div class="chat-widget-quick-replies" id="chatWidgetQuickReplies">
                        <div class="chat-widget-quick-reply" data-message="أريد معرفة الدورات المتاحة">الدورات المتاحة</div>
                        <div class="chat-widget-quick-reply" data-message="كيف أسجل في دورة؟">كيف أسجل؟</div>
                        <div class="chat-widget-quick-reply" data-message="ما هي أسعار الدورات؟">الأسعار</div>
                        <div class="chat-widget-quick-reply" data-message="أريد التواصل مع الإدارة">تواصل مع الإدارة</div>
                    </div>
                    <div class="chat-widget-input">
                        <input type="text" id="chatWidgetInput" placeholder="اكتب رسالتك هنا..." />
                        <button class="chat-widget-send" id="chatWidgetSend">➤</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', widgetHTML);
    }

    bindEvents() {
        const icon = document.getElementById('chatWidgetIcon');
        const container = document.getElementById('chatWidgetContainer');
        const closeBtn = document.getElementById('chatWidgetClose');
        const input = document.getElementById('chatWidgetInput');
        const sendBtn = document.getElementById('chatWidgetSend');
        const quickReplies = document.getElementById('chatWidgetQuickReplies');

        // Toggle chat widget
        icon.addEventListener('click', () => this.toggleWidget());

        // Close chat widget
        closeBtn.addEventListener('click', () => this.closeWidget());

        // Send message on button click
        sendBtn.addEventListener('click', () => this.sendMessage());

        // Send message on Enter key
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // Quick reply buttons
        quickReplies.addEventListener('click', (e) => {
            if (e.target.classList.contains('chat-widget-quick-reply')) {
                const message = e.target.getAttribute('data-message');
                this.addMessage(message, 'user');
                this.processMessage(message);
            }
        });

        // Close on outside click
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target) && !icon.contains(e.target) && this.isOpen) {
                this.closeWidget();
            }
        });
    }

    toggleWidget() {
        if (this.isOpen) {
            this.closeWidget();
        } else {
            this.openWidget();
        }
    }

    openWidget() {
        const container = document.getElementById('chatWidgetContainer');
        container.classList.add('active');
        this.isOpen = true;
        
        // Focus on input
        setTimeout(() => {
            document.getElementById('chatWidgetInput').focus();
        }, 300);
    }

    closeWidget() {
        const container = document.getElementById('chatWidgetContainer');
        container.classList.remove('active');
        this.isOpen = false;
    }

    sendMessage() {
        const input = document.getElementById('chatWidgetInput');
        const message = input.value.trim();
        
        if (message) {
            this.addMessage(message, 'user');
            this.processMessage(message);
            input.value = '';
        }
    }

    addMessage(text, sender) {
        const messagesContainer = document.getElementById('chatWidgetMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-widget-message ${sender}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'chat-widget-avatar';
        avatar.textContent = sender === 'bot' ? '🤖' : '👤';
        
        const content = document.createElement('div');
        content.className = 'chat-widget-message-content';
        content.textContent = text;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        messagesContainer.appendChild(messageDiv);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Save to history
        this.messages.push({ text, sender, timestamp: new Date() });
        this.saveChatHistory();
    }

    showTyping() {
        const typing = document.getElementById('chatWidgetTyping');
        typing.classList.add('active');
    }

    hideTyping() {
        const typing = document.getElementById('chatWidgetTyping');
        typing.classList.remove('active');
    }

    processMessage(message) {
        this.showTyping();
        
        // Simulate processing delay
        setTimeout(() => {
            this.hideTyping();
            const response = this.getBotResponse(message);
            this.addMessage(response, 'bot');
        }, 1000 + Math.random() * 2000);
    }

    getBotResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Predefined responses
        const responses = {
            'مرحبا': 'مرحباً بك في معهد دارين! كيف يمكنني مساعدتك اليوم؟',
            'السلام عليكم': 'وعليكم السلام ورحمة الله وبركاته! أهلاً وسهلاً بك في معهد دارين.',
            'أريد معرفة الدورات المتاحة': 'لدينا مجموعة متنوعة من الدورات:\n• دورات القرآن الكريم\n• دورات اللغة العربية\n• دورات الحفظ والتجويد\n• دورات العلوم الشرعية\n\nهل تريد معرفة تفاصيل دورة معينة؟',
            'كيف أسجل في دورة؟': 'يمكنك التسجيل في الدورات بعدة طرق:\n1. عبر موقعنا الإلكتروني\n2. زيارة المعهد مباشرة\n3. الاتصال بنا على الرقم: +966 50 123 4567\n4. عبر صفحة "اتصل بنا"\n\nهل تريد مساعدة في التسجيل؟',
            'ما هي أسعار الدورات؟': 'أسعار الدورات تختلف حسب نوع الدورة ومدة الدراسة:\n• الدورات الأساسية: 200-500 ريال\n• الدورات المتقدمة: 500-1000 ريال\n• الدورات الخاصة: حسب الطلب\n\nللحصول على تفاصيل دقيقة، يمكنك التواصل معنا.',
            'أريد التواصل مع الإدارة': 'يمكنك التواصل مع إدارة المعهد عبر:\n• الهاتف: +966 50 123 4567\n• البريد الإلكتروني: <EMAIL>\n• زيارة المعهد: [عنوان المعهد]\n• صفحة "اتصل بنا" على الموقع\n\nسنكون سعداء بمساعدتك!',
            'شكرا': 'العفو! نحن سعداء بمساعدتك. إذا كان لديك أي استفسارات أخرى، لا تتردد في السؤال.',
            'مع السلامة': 'مع السلامة! نتمنى لك يوماً سعيداً ونرجو أن نراك قريباً في معهد دارين.',
            'أين يقع المعهد': 'يقع معهد دارين في [عنوان المعهد]. يمكنك الوصول إلينا بسهولة عبر:\n• وسائل النقل العام\n• السيارة الخاصة\n• التطبيقات الإلكترونية\n\nهل تريد خريطة توضح موقع المعهد؟',
            'ما هي أوقات العمل': 'أوقات عمل المعهد:\n• الأحد - الخميس: 8:00 ص - 10:00 م\n• الجمعة: 2:00 م - 10:00 م\n• السبت: 8:00 ص - 10:00 م\n\nنستقبل الطلاب على مدار اليوم!'
        };

        // Check for exact matches first
        for (const [key, response] of Object.entries(responses)) {
            if (lowerMessage.includes(key.toLowerCase())) {
                return response;
            }
        }

        // Check for partial matches
        if (lowerMessage.includes('دورة') || lowerMessage.includes('دورات')) {
            return 'لدينا العديد من الدورات المتنوعة. هل تريد معرفة:\n• الدورات المتاحة حالياً؟\n• أسعار الدورات؟\n• كيفية التسجيل؟';
        }

        if (lowerMessage.includes('سعر') || lowerMessage.includes('تكلفة') || lowerMessage.includes('ثمن')) {
            return 'أسعار الدورات تختلف حسب نوع الدورة. هل تريد معرفة سعر دورة معينة أم جميع الأسعار؟';
        }

        if (lowerMessage.includes('سجل') || lowerMessage.includes('تسجيل') || lowerMessage.includes('انضم')) {
            return 'يمكنك التسجيل في الدورات عبر موقعنا أو زيارة المعهد. هل تريد معرفة خطوات التسجيل؟';
        }

        if (lowerMessage.includes('موقع') || lowerMessage.includes('أين') || lowerMessage.includes('عنوان')) {
            return 'يقع معهد دارين في [عنوان المعهد]. هل تريد خريطة توضح الموقع؟';
        }

        // Default response
        return 'شكراً لك على رسالتك! كيف يمكنني مساعدتك؟ يمكنك:\n• معرفة الدورات المتاحة\n• الاستفسار عن الأسعار\n• معرفة كيفية التسجيل\n• التواصل مع الإدارة';
    }

    showWelcomeMessage() {
        setTimeout(() => {
            this.addMessage('مرحباً بك في معهد دارين! كيف يمكنني مساعدتك اليوم؟', 'bot');
        }, 500);
    }

    saveChatHistory() {
        localStorage.setItem('chatWidgetHistory', JSON.stringify(this.messages));
        
        // إرسال الرسائل لمدير النظام
        this.sendToAdmin();
    }
    
    // دالة إرسال الرسائل لمدير النظام
    sendToAdmin() {
        try {
            // حفظ الرسائل في localStorage للوصول من صفحة admin
            localStorage.setItem('chatMessagesForAdmin', JSON.stringify(this.messages));
            
            // حفظ معلومات الجلسة
            const sessionInfo = {
                id: Date.now(),
                user: 'مستخدم جديد',
                time: new Date().toLocaleString('ar-SA'),
                preview: this.messages[this.messages.length - 1]?.text || 'رسالة جديدة',
                messages: this.messages,
                timestamp: new Date()
            };
            
            // الحصول على الجلسات الموجودة
            let adminSessions = JSON.parse(localStorage.getItem('adminChatSessions') || '[]');
            
            // إضافة الجلسة الجديدة
            adminSessions.push(sessionInfo);
            
            // حفظ الجلسات
            localStorage.setItem('adminChatSessions', JSON.stringify(adminSessions));
            
            console.log('تم إرسال الرسائل لمدير النظام');
        } catch (error) {
            console.error('خطأ في إرسال الرسائل لمدير النظام:', error);
        }
    }

    loadChatHistory() {
        const history = localStorage.getItem('chatWidgetHistory');
        if (history) {
            this.messages = JSON.parse(history);
            // Display last 10 messages without saving them again
            const recentMessages = this.messages.slice(-10);
            const messagesContainer = document.getElementById('chatWidgetMessages');

            recentMessages.forEach(msg => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-widget-message ${msg.sender}`;

                const avatar = document.createElement('div');
                avatar.className = 'chat-widget-avatar';
                avatar.textContent = msg.sender === 'bot' ? '🤖' : '👤';

                const content = document.createElement('div');
                content.className = 'chat-widget-message-content';
                content.textContent = msg.text;

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(content);
                messagesContainer.appendChild(messageDiv);
            });

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }
}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatWidget();
});
