<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تعرف على منصة دارين التعليمية - رائدة في مجال التعليم الرقمي والتطوير الأكاديمي. اكتشف قصتنا، رؤيتنا، رسالتنا، قيمنا وفريق العمل المتميز الذي يقف خلف نجاحنا.">
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Noto+Kufi+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <title>من نحن - منصة دارين التعليمية</title>
    <style>
        /* تأثيرات الحركة */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        
        @keyframes slideInRight {
            from { transform: translateX(50px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideInLeft {
            from { transform: translateX(-50px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes countUp {
            0% { opacity: 0; transform: translateY(15px); }
            50% { opacity: 0.5; transform: translateY(7px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0deg); }
        }
        
        @keyframes glow {
            0% { text-shadow: 0 0 5px rgba(72, 206, 29, 0.5); }
            50% { text-shadow: 0 0 20px rgba(72, 206, 29, 0.8), 0 0 30px rgba(72, 206, 29, 0.6); }
            100% { text-shadow: 0 0 5px rgba(72, 206, 29, 0.5); }
        }
        
        .section-title, .section-subtitle {
            animation: fadeIn 0.8s ease forwards;
        }
        
        .stat-card, .value-card {
            animation: scaleIn 0.6s ease forwards;
            animation-delay: calc(var(--animation-order, 0) * 0.1s);
            opacity: 0;
        }
        
        .stat-card h3 {
            animation: countUp 1.2s forwards;
            animation-delay: calc(var(--animation-order, 0) * 0.2s);
        }
        
        .stat-icon {
            animation: pulse 2s infinite;
        }
        
        .about-hero {
            background: linear-gradient(135deg, #151413 0%, #1a1918 50%, #151413 100%);
            padding: 80px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .about-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 30% 70%, rgba(72, 206, 29, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .about-content {
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        
        .stat-card {
            background: rgba(35, 35, 35, 0.8);
            padding: 30px 20px;
            border-radius: 20px;
            border: 1px solid rgba(72, 206, 29, 0.2);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(72, 206, 29, 0.2);
        }
        
        .values-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 30px 10px;
                padding: 0 10px;
            }
        
        .value-card {
            background: rgba(35, 35, 35, 0.6);
            padding: 25px 20px;
            border-radius: 15px;
            border: 1px solid rgba(72, 206, 29, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            margin: 0 5px;
        }
        
        .value-card:hover {
            border-color: rgba(72, 206, 29, 0.4);
            background: rgba(35, 35, 35, 0.8);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(72, 206, 29, 0.2);
        }
        
        .section-title {
            color: #48ce1d;
            font-family: 'Cairo', Arial, sans-serif;
            font-weight: 900;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .section-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 1.2rem;
            margin-bottom: 40px;
            text-align: center;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .about-hero {
                padding: 60px 20px 40px;
            }
            .section-title {
                font-size: 1.8rem;
                margin-bottom: 15px;
                line-height: 1.3;
            }
            .stats-grid {
                display: grid !important;
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 15px !important;
                margin-top: 20px !important;
                padding: 0 10px !important;
                flex-wrap: wrap !important;
            }
            .stats-grid .stat-card {
                width: auto !important;
                padding: 10px !important;
                margin: 0 !important;
                transform: translateY(0) rotate(0deg) !important;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;
                text-align: center !important;
            }
            .stats-grid .stat-card .stat-number {
                font-size: 1.5rem !important;
                margin-bottom: 3px !important;
            }
            .stats-grid .stat-card p {
                font-size: 0.8rem !important;
                margin: 0 !important;
                line-height: 1.2 !important;
            }
            .values-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                margin: 20px 0;
                padding: 0 15px;
            }
            .section-subtitle {
                font-size: 1rem;
                margin: 0 0 15px;
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="index.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="courses.html">الدورات</a></li>
                <li><a href="contact.html">اتصل بنا</a></li>
                <li><a href="about.html">من نحن</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="index.html" class="login-btn-links">الرئيسية</a></li>
            <li><a href="courses.html" class="login-btn-links">الدورات</a></li>
            <li><a href="contact.html" class="login-btn-links">اتصل بنا</a></li>
            <li><a href="about.html" class="login-btn-links">من نحن</a></li>
        </ul>
        <div class="mobile-actions">
            <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
            <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
        </div>
        <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
    </div>

    <!-- قسم البطل الرئيسي -->
    <section class="about-hero" style="background: linear-gradient(135deg, #121212 0%, #1a1a1a 50%, #121212 100%); position: relative; overflow: hidden;">
        <!-- تم إزالة الأشكال الزخرفية المتحركة في الخلفية -->
        
        <div class="about-content" style="position: relative; z-index: 2;">
            <div style="position: relative; display: inline-block; margin-bottom: 30px;">
                <h1 class="section-title" style="position: relative; display: inline-block; padding: 0 15px 20px; font-size: 4rem; font-weight: 900; letter-spacing: 1px; text-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);">
                    <span style="background: linear-gradient(90deg, #fff, #48ce1d 70%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 800;">من نحن</span>
                    <span style="position: absolute; bottom: 0; left: 50%; transform: translateX(-50%); width: 120px; height: 4px; background: linear-gradient(90deg, transparent, #48ce1d, transparent); border-radius: 2px; box-shadow: 0 0 15px rgba(72, 206, 29, 0.7);"></span>
                </h1>
                <!-- زخرفة حول العنوان -->
                <div style="position: absolute; top: -10px; right: -20px; width: 40px; height: 40px; border-top: 2px solid #48ce1d; border-right: 2px solid #48ce1d; opacity: 0.5;"></div>
                <div style="position: absolute; bottom: 10px; left: -20px; width: 40px; height: 40px; border-bottom: 2px solid #48ce1d; border-left: 2px solid #48ce1d; opacity: 0.5;"></div>
            </div>
            
            <p class="section-subtitle" style="max-width: 800px; margin: 0 auto 50px; line-height: 1.9; font-size: 1.4rem; color: rgba(255, 255, 255, 0.9);">
                <span style="font-weight: 700; color: #fff; text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5); position: relative; display: inline-block;">
                    منصة دارين التعليمية
                    <span style="position: absolute; bottom: -3px; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, transparent, rgba(72, 206, 29, 0.5), transparent);"></span>
                </span>
                - رائدون في التعليم الرقمي والتطوير الأكاديمي. نسعى لتقديم تعليم عالي الجودة يواكب متطلبات العصر الحديث ويلبي احتياجات الطلاب في جميع المراحل الدراسية
            </p>
            
            <!-- إحصائيات سريعة -->
            <div class="stats-grid" style="display: flex; justify-content: center; gap: 20px; margin-top: 70px; padding: 0 20px;">
                <!-- تم إزالة الزخرفة خلف الإحصائيات -->
                
                <div class="stat-card" style="position: relative; overflow: hidden; background: rgba(35, 35, 35, 0.8); backdrop-filter: blur(5px); border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); transform: translateY(0) rotate(2deg); transition: all 0.3s ease; padding: 10px 8px; width: 140px; flex-shrink: 0;" 
                    onmouseover="this.style.transform='translateY(-3px) scale(1.01) rotate(2deg)'; this.style.boxShadow='0 4px 12px rgba(72, 206, 29, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 0 5px rgba(72, 206, 29, 0.6)';"
                    onmouseout="this.style.transform='translateY(0) scale(1) rotate(2deg)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 1px 3px rgba(72, 206, 29, 0.3)'">
                    <h3 class="stat-number" style="color: #48ce1d; font-size: 1.8rem; margin-bottom: 3px; font-weight: 800; font-family: 'Arial', sans-serif; text-shadow: 0 1px 3px rgba(72, 206, 29, 0.3); text-align: center;">
                        <span class="counter-fixed">4</span>+
                    </h3>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.8rem; font-weight: 600; font-family: 'Cairo', sans-serif; text-align: center;">
                        سنوات من الخبرة
                    </p>
                </div>
                
                <div class="stat-card" style="position: relative; overflow: hidden; background: rgba(35, 35, 35, 0.8); backdrop-filter: blur(5px); border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); transform: translateY(0) rotate(-2deg); transition: all 0.3s ease; padding: 10px 8px; width: 140px; flex-shrink: 0;" 
                    onmouseover="this.style.transform='translateY(-3px) scale(1.01) rotate(-2deg)'; this.style.boxShadow='0 4px 12px rgba(72, 206, 29, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 0 5px rgba(72, 206, 29, 0.6)';"
                    onmouseout="this.style.transform='translateY(0) scale(1) rotate(-2deg)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 1px 3px rgba(72, 206, 29, 0.3)'">
                    <h3 class="stat-number" style="color: #48ce1d; font-size: 1.8rem; margin-bottom: 3px; font-weight: 800; font-family: 'Arial', sans-serif; text-shadow: 0 1px 3px rgba(72, 206, 29, 0.3); text-align: center;">
                        <span class="counter-fixed">867</span>
                    </h3>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.8rem; font-weight: 600; font-family: 'Cairo', sans-serif; text-align: center;">
                        طالب متخرج
                    </p>
                </div>
                
                <div class="stat-card" style="position: relative; overflow: hidden; background: rgba(35, 35, 35, 0.8); backdrop-filter: blur(5px); border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); transform: translateY(0) rotate(2deg); transition: all 0.3s ease; padding: 10px 8px; width: 140px; flex-shrink: 0;" 
                    onmouseover="this.style.transform='translateY(-3px) scale(1.01) rotate(2deg)'; this.style.boxShadow='0 4px 12px rgba(72, 206, 29, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 0 5px rgba(72, 206, 29, 0.6)';"
                    onmouseout="this.style.transform='translateY(0) scale(1) rotate(2deg)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 1px 3px rgba(72, 206, 29, 0.3)'">
                    <h3 class="stat-number" style="color: #48ce1d; font-size: 1.8rem; margin-bottom: 3px; font-weight: 800; font-family: 'Arial', sans-serif; text-shadow: 0 1px 3px rgba(72, 206, 29, 0.3); text-align: center;">
                        <span class="counter-fixed">37</span>
                    </h3>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.8rem; font-weight: 600; font-family: 'Cairo', sans-serif; text-align: center;">
                        دورة تعليمية
                    </p>
                </div>
                
                <div class="stat-card" style="position: relative; overflow: hidden; background: rgba(35, 35, 35, 0.8); backdrop-filter: blur(5px); border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); transform: translateY(0) rotate(-2deg); transition: all 0.3s ease; padding: 10px 8px; width: 140px; flex-shrink: 0;" 
                    onmouseover="this.style.transform='translateY(-3px) scale(1.01) rotate(-2deg)'; this.style.boxShadow='0 4px 12px rgba(72, 206, 29, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 0 5px rgba(72, 206, 29, 0.6)';"
                    onmouseout="this.style.transform='translateY(0) scale(1) rotate(-2deg)'; this.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.2)'; this.querySelector('.stat-number').style.textShadow='0 1px 3px rgba(72, 206, 29, 0.3)'">
                    <h3 class="stat-number" style="color: #48ce1d; font-size: 1.8rem; margin-bottom: 3px; font-weight: 800; font-family: 'Arial', sans-serif; text-shadow: 0 1px 3px rgba(72, 206, 29, 0.3); text-align: center;">
                        <span class="counter-fixed">97</span>%
                    </h3>
                    <p style="color: rgba(255, 255, 255, 0.95); font-size: 0.8rem; font-weight: 600; font-family: 'Cairo', sans-serif; text-align: center;">
                        نسبة رضا الطلاب
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم قصتنا -->
    <section style="background: #191717; padding: 80px 20px;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <h2 class="section-title">قصتنا</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 40px; align-items: center;">
                <div style="flex: 1 1 400px;">
                    <p style="color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1.1rem; line-height: 1.8; text-align: justify;">
                        بدأت قصة منصة دارين التعليمية في عام 2013 كمبادرة من مجموعة من المعلمين المتميزين الذين آمنوا بأهمية توفير تعليم عالي الجودة لجميع الطلاب بغض النظر عن مكان تواجدهم.
                        <br><br>
                        مع مرور السنوات، تطورت المنصة من مجرد فكرة إلى منصة تعليمية متكاملة تخدم آلاف الطلاب في مختلف المراحل الدراسية. نفخر اليوم بأننا أصبحنا من رواد التعليم الإلكتروني في المنطقة، ونسعى دائماً لتطوير خدماتنا وتوسيع نطاق تأثيرنا.
                        <br><br>
                        نؤمن في منصة دارين بأن التعليم هو حق للجميع، ونعمل جاهدين على توفير محتوى تعليمي متميز يساعد الطلاب على تحقيق أهدافهم وطموحاتهم.
                    </p>
                </div>
                <div style="flex: 1 1 400px;">
                    <img src="../assets/images/Dr3.png" alt="قصة منصة دارين" style="width: 100%;">
                </div>
            </div>
        </div>
    </section>

    <!-- قسم رؤيتنا ورسالتنا وأهدافنا -->
    <section style="background: linear-gradient(135deg, #151413 0%, #1a1918 50%, #151413 100%); padding: 100px 20px;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <h2 class="section-title" style="margin-bottom: 50px;">مسيرتنا التعليمية</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 30px; justify-content: center;">
                <div style="flex: 1 1 300px; background: rgba(35, 35, 35, 0.8); padding: 40px; border-radius: 15px; border: 1px solid rgba(72, 206, 29, 0.3); box-shadow: 0 10px 30px rgba(0,0,0,0.2); transition: all 0.3s ease;" 
                    onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 35px rgba(72, 206, 29, 0.2)';"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)';">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <span style="font-size: 3rem; color: #48ce1d; text-shadow: 0 0 10px rgba(72, 206, 29, 0.3);">⚡</span>
                    </div>
                    <h2 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 2rem; margin-bottom: 25px; text-align: center; text-shadow: 0 2px 5px rgba(0,0,0,0.3);">رؤيتنا</h2>
                    <p style="color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1.1rem; line-height: 1.8; text-align: center; position: relative; padding: 0 10px;">
                        <span style="position: absolute; top: -10px; right: 0; font-size: 1.5rem; color: rgba(72, 206, 29, 0.5);">❝</span>
                        أن نكون المنصة التعليمية الرائدة في تقديم تعليم رقمي متميز يساهم في بناء جيل واعٍ ومبدع قادر على مواجهة تحديات المستقبل.
                        <span style="position: absolute; bottom: -10px; left: 0; font-size: 1.5rem; color: rgba(72, 206, 29, 0.5);">❞</span>
                    </p>
                </div>
                
                <div style="flex: 1 1 300px; background: rgba(35, 35, 35, 0.8); padding: 40px; border-radius: 15px; border: 1px solid rgba(72, 206, 29, 0.3); box-shadow: 0 10px 30px rgba(0,0,0,0.2); transition: all 0.3s ease;" 
                    onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 35px rgba(72, 206, 29, 0.2)';"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)';">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <span style="font-size: 3rem; color: #48ce1d; text-shadow: 0 0 10px rgba(72, 206, 29, 0.3);">🚀</span>
                    </div>
                    <h2 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 2rem; margin-bottom: 25px; text-align: center; text-shadow: 0 2px 5px rgba(0,0,0,0.3);">رسالتنا</h2>
                    <p style="color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1.1rem; line-height: 1.8; text-align: center; position: relative; padding: 0 10px;">
                        <span style="position: absolute; top: -10px; right: 0; font-size: 1.5rem; color: rgba(72, 206, 29, 0.5);">❝</span>
                        توفير بيئة تعليمية تفاعلية تجمع بين جودة المحتوى وتميز المعلمين وسهولة الوصول، لتمكين الطلاب من تحقيق التفوق الدراسي وبناء مستقبل أفضل.
                        <span style="position: absolute; bottom: -10px; left: 0; font-size: 1.5rem; color: rgba(72, 206, 29, 0.5);">❞</span>
                    </p>
                </div>
                
                <div style="flex: 1 1 300px; background: rgba(35, 35, 35, 0.8); padding: 40px; border-radius: 15px; border: 1px solid rgba(72, 206, 29, 0.3); box-shadow: 0 10px 30px rgba(0,0,0,0.2); transition: all 0.3s ease;" 
                    onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 15px 35px rgba(72, 206, 29, 0.2)';"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)';">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <span style="font-size: 3rem; color: #48ce1d; text-shadow: 0 0 10px rgba(72, 206, 29, 0.3);">🎯</span>
                    </div>
                    <h2 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 2rem; margin-bottom: 25px; text-align: center; text-shadow: 0 2px 5px rgba(0,0,0,0.3);">أهدافنا</h2>
                    <p style="color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1.1rem; line-height: 1.8; text-align: center; position: relative; padding: 0 10px;">
                        <span style="position: absolute; top: -10px; right: 0; font-size: 1.5rem; color: rgba(72, 206, 29, 0.5);">❝</span>
                        نسعى لتحقيق التميز في التعليم الرقمي، وتوفير فرص تعليمية متكافئة للجميع، وتطوير مهارات الطلاب بما يتناسب مع متطلبات سوق العمل المستقبلية.
                        <span style="position: absolute; bottom: -10px; left: 0; font-size: 1.5rem; color: rgba(72, 206, 29, 0.5);">❞</span>
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم قيمنا -->
    <section style="background: #191717; padding: 80px 20px;">
        <div style="max-width: 1200px; margin: 0 auto;">
            <h2 class="section-title">قيمنا</h2>
            <p class="section-subtitle">المبادئ التي نؤمن بها ونعمل وفقاً لها في منصة دارين التعليمية</p>
            
            <div class="values-grid">
                <div class="value-card">
                    <div style="font-size: 3rem; color: #48ce1d; margin-bottom: 20px;">🎯</div>
                    <h3 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 1.5rem; margin-bottom: 15px;">التميز</h3>
                    <p style="color: rgba(255, 255, 255, 0.8); font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1rem; line-height: 1.6;">
                        نسعى دائماً لتقديم أفضل مستوى من الخدمات التعليمية والارتقاء بمستوى الطلاب أكاديمياً وشخصياً.
                    </p>
                </div>
                <div class="value-card">
                    <div style="font-size: 3rem; color: #48ce1d; margin-bottom: 20px;">🤝</div>
                    <h3 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 1.5rem; margin-bottom: 15px;">التعاون</h3>
                    <p style="color: rgba(255, 255, 255, 0.8); font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1rem; line-height: 1.6;">
                        نؤمن بأهمية العمل الجماعي والتعاون بين الطلاب والمعلمين وأولياء الأمور لتحقيق أفضل النتائج.
                    </p>
                </div>
                <div class="value-card">
                    <div style="font-size: 3rem; color: #48ce1d; margin-bottom: 20px;">💡</div>
                    <h3 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 1.5rem; margin-bottom: 15px;">الإبداع</h3>
                    <p style="color: rgba(255, 255, 255, 0.8); font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1rem; line-height: 1.6;">
                        نشجع التفكير الإبداعي والابتكار في التعليم، ونسعى دائماً لتطوير أساليب تعليمية جديدة ومبتكرة.
                    </p>
                </div>
                <div class="value-card">
                    <div style="font-size: 3rem; color: #48ce1d; margin-bottom: 20px;">⚖️</div>
                    <h3 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 1.5rem; margin-bottom: 15px;">المساواة</h3>
                    <p style="color: rgba(255, 255, 255, 0.8); font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1rem; line-height: 1.6;">
                        نؤمن بحق الجميع في الحصول على تعليم عالي الجودة بغض النظر عن خلفياتهم الاجتماعية أو الاقتصادية.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- تم حذف قسم فريق العمل -->

    <!-- قسم تواصل معنا - تصميم محسن -->
    <section style="background: linear-gradient(135deg, #151413 0%, #1a1918 50%, #151413 100%); padding: 100px 20px; text-align: center; position: relative; overflow: hidden;">
        <!-- زخارف خلفية -->
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle at 30% 70%, rgba(72, 206, 29, 0.1) 0%, transparent 50%); pointer-events: none;"></div>
        <div style="position: absolute; top: 20px; right: 20px; width: 150px; height: 150px; border: 2px solid rgba(72, 206, 29, 0.1); border-radius: 50%; pointer-events: none;"></div>
        <div style="position: absolute; bottom: 50px; left: 50px; width: 100px; height: 100px; border: 2px solid rgba(72, 206, 29, 0.1); border-radius: 50%; pointer-events: none;"></div>
        
        <div style="max-width: 1000px; margin: 0 auto; position: relative; z-index: 2;">
            <!-- عنوان القسم مع تأثيرات -->
            <div style="position: relative; display: inline-block; margin-bottom: 40px;">
                <h2 class="section-title" style="font-size: 3.5rem; margin-bottom: 10px; background: linear-gradient(90deg, #fff, #48ce1d 70%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 800; text-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);">
                    تواصل معنا
                    <span style="position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 150px; height: 4px; background: linear-gradient(90deg, transparent, #48ce1d, transparent); border-radius: 2px; box-shadow: 0 0 15px rgba(72, 206, 29, 0.7);"></span>
                </h2>
                <!-- زخرفة حول العنوان -->
                <div style="position: absolute; top: -10px; right: -20px; width: 40px; height: 40px; border-top: 2px solid #48ce1d; border-right: 2px solid #48ce1d; opacity: 0.5;"></div>
                <div style="position: absolute; bottom: -20px; left: -20px; width: 40px; height: 40px; border-bottom: 2px solid #48ce1d; border-left: 2px solid #48ce1d; opacity: 0.5;"></div>
            </div>
            
            <!-- وصف محسن -->
            <p style="color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 1.3rem; line-height: 1.9; margin-bottom: 50px; max-width: 800px; margin-left: auto; margin-right: auto; text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);">
                <span style="position: relative; display: inline-block; margin-bottom: 5px;">
                    <span style="font-weight: 700; color: #48ce1d;">نحن هنا لمساعدتك!</span>
                    <span style="position: absolute; bottom: -3px; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, transparent, rgba(72, 206, 29, 0.5), transparent);"></span>
                </span>
                <br>
                نسعد بالإجابة على جميع استفساراتكم وتقديم الدعم اللازم لكم. يمكنكم التواصل معنا عبر القنوات المختلفة أو زيارتنا في مقر المنصة.
            </p>
            
            <!-- تم حذف بطاقات وسائل التواصل بناءً على طلب المستخدم -->
            
            <!-- أزرار التواصل المحسنة -->
            <div style="display: flex; flex-wrap: wrap; gap: 30px; justify-content: center; margin-top: 40px;">
                <a href="contact.html" style="background: linear-gradient(135deg, #48ce1d, #3db517); color: #191717; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 1.2rem; padding: 18px 40px; border-radius: 50px; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(72, 206, 29, 0.3); position: relative; overflow: hidden;" 
                    onmouseover="this.style.transform='translateY(-5px) scale(1.05)'; this.style.boxShadow='0 15px 30px rgba(72, 206, 29, 0.4)'; this.querySelector('.btn-shine').style.left='150%'" 
                    onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 5px 15px rgba(72, 206, 29, 0.3)'; this.querySelector('.btn-shine').style.left='-50%'">
                    <span class="btn-shine" style="position: absolute; top: 0; left: -50%; width: 50px; height: 100%; background: rgba(255, 255, 255, 0.3); transform: skewX(-25deg); transition: all 0.5s ease;"></span>
                    <span style="display: inline-flex; align-items: center; position: relative; z-index: 1;">تواصل معنا الآن <span style="margin-right: 10px; font-size: 1.3rem;">👋</span></span>
                </a>
                
                <a href="register.html" style="background: transparent; color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 700; font-size: 1.2rem; padding: 18px 40px; border-radius: 50px; text-decoration: none; transition: all 0.3s ease; border: 2px solid #48ce1d; position: relative; overflow: hidden;" 
                    onmouseover="this.style.backgroundColor='rgba(72, 206, 29, 0.1)'; this.style.transform='translateY(-5px) scale(1.05)'; this.style.borderColor='#48ce1d'; this.style.boxShadow='0 10px 20px rgba(72, 206, 29, 0.2)'" 
                    onmouseout="this.style.backgroundColor='transparent'; this.style.transform='translateY(0) scale(1)'; this.style.borderColor='#48ce1d'; this.style.boxShadow='none'">
                    <span style="display: inline-flex; align-items: center;">انضم إلينا <span style="margin-right: 10px; font-size: 1.3rem;">🚀</span></span>
                </a>
            </div>
            
            <!-- وسائل التواصل الاجتماعي -->
            <div style="margin-top: 60px;">
                <p style="color: #fff; font-family: 'Cairo', Arial, sans-serif; font-size: 1.2rem; margin-bottom: 20px; opacity: 0.9;">تابعنا على وسائل التواصل الاجتماعي</p>
                <div style="display: flex; gap: 20px; justify-content: center;">
                    <a href="#" style="width: 50px; height: 50px; border-radius: 50%; background: rgba(35, 35, 35, 0.8); display: flex; align-items: center; justify-content: center; border: 1px solid rgba(72, 206, 29, 0.2); transition: all 0.3s ease;" 
                        onmouseover="this.style.transform='translateY(-5px)'; this.style.backgroundColor='rgba(72, 206, 29, 0.2)';" 
                        onmouseout="this.style.transform='translateY(0)'; this.style.backgroundColor='rgba(35, 35, 35, 0.8)';">
                        <img src="../assets/images/instagram.png" alt="انستغرام" style="width: 25px; height: 25px;">
                    </a>
                    <a href="#" style="width: 50px; height: 50px; border-radius: 50%; background: rgba(35, 35, 35, 0.8); display: flex; align-items: center; justify-content: center; border: 1px solid rgba(72, 206, 29, 0.2); transition: all 0.3s ease;" 
                        onmouseover="this.style.transform='translateY(-5px)'; this.style.backgroundColor='rgba(72, 206, 29, 0.2)';" 
                        onmouseout="this.style.transform='translateY(0)'; this.style.backgroundColor='rgba(35, 35, 35, 0.8)';">
                        <img src="../assets/images/telegram.png" alt="تيليجرام" style="width: 25px; height: 25px;">
                    </a>
                    <a href="#" style="width: 50px; height: 50px; border-radius: 50%; background: rgba(35, 35, 35, 0.8); display: flex; align-items: center; justify-content: center; border: 1px solid rgba(72, 206, 29, 0.2); transition: all 0.3s ease;" 
                        onmouseover="this.style.transform='translateY(-5px)'; this.style.backgroundColor='rgba(72, 206, 29, 0.2)';" 
                        onmouseout="this.style.transform='translateY(0)'; this.style.backgroundColor='rgba(35, 35, 35, 0.8)';">
                        <img src="../assets/images/tiktok.png" alt="تيك توك" style="width: 25px; height: 25px;">
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }
        
        // تطبيق ترتيب الحركة على البطاقات
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق ترتيب الحركة على بطاقات الإحصائيات
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.setProperty('--animation-order', index);
                
                // إضافة تأثير حركة للخطوط تحت النص عند التحويم
                card.addEventListener('mouseover', function() {
                    const underlines = card.querySelectorAll('span[style*="position: absolute; bottom"]');
                    underlines.forEach(line => {
                        line.style.width = '100%';
                    });
                });
                
                card.addEventListener('mouseout', function() {
                    const underlines = card.querySelectorAll('span[style*="position: absolute; bottom"]');
                    underlines.forEach(line => {
                        line.style.width = '0';
                    });
                });
            });
            
            // تطبيق ترتيب الحركة على بطاقات القيم
            const valueCards = document.querySelectorAll('.value-card');
            valueCards.forEach((card, index) => {
                card.style.setProperty('--animation-order', index);
            });
            
            // تطبيق تأثيرات الحركة عند التمرير
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.visibility = 'visible';
                        observer.unobserve(entry.target);
                        
                        // تطبيق تأثير العد التصاعدي للأرقام
                        if (entry.target.classList.contains('about-hero')) {
                            animateStatNumbers();
                            
                            // تم إزالة تأثيرات العناصر الزخرفية
                            
                            // تم إزالة تأثير الحركة للعنوان الرئيسي
                            
                            // إضافة تأثيرات للزخارف حول العنوان
                            const titleDecorations = document.querySelectorAll('.about-content > div > div');
                            titleDecorations.forEach(decoration => {
                                decoration.style.animation = 'fadeIn 1.5s ease-in-out forwards';
                            });
                        }
                    }
                });
            }, { threshold: 0.1 });
            
            // مراقبة جميع الأقسام
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });
            
            // وظيفة لتحريك الأرقام في الإحصائيات
            function animateStatNumbers() {
                const statNumbers = document.querySelectorAll('.counter');
                
                statNumbers.forEach(numElement => {
                    const finalValue = numElement.innerText;
                    let value = parseInt(finalValue);
                    
                    // تحديد سرعة العد بناءً على القيمة النهائية
                    const duration = 2000; // مدة العد بالمللي ثانية
                    const steps = 50; // عدد خطوات العد
                    const increment = value / steps;
                    let currentValue = 0;
                    let counter = 0;
                    
                    // بدء العد التصاعدي
                    const interval = setInterval(() => {
                        counter++;
                        currentValue = Math.ceil(counter * increment);
                        
                        if (currentValue >= value) {
                            clearInterval(interval);
                            numElement.innerText = finalValue; // استعادة النص الأصلي بالكامل
                        } else {
                            numElement.innerText = currentValue;
                        }
                    }, duration / steps);
                });
                
                // لا نقوم بتحريك الأرقام الثابتة
                // العناصر ذات الكلاس counter-fixed تبقى ثابتة
            }
            
            // تطبيق تأثير العد التصاعدي عند تحميل الصفحة بعد تأخير قصير
            setTimeout(animateStatNumbers, 1000);
        });
    </script>
    </body>
    </html>
