<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم مدير النظام - منصة دارين التعليمية</title>
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Noto+Kufi+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* تعديل موضع القائمة في صفحة مدير النظام */
        .nav {
            margin-top: 15px;
        }
        
        /* أنماط قسم المحادثات */
        .chat-admin-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .chat-admin-header h2 {
            color: #48ce1d;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .chat-admin-header p {
            color: #ccc;
            font-size: 1.1rem;
        }
        
        .chat-admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: rgba(72, 206, 29, 0.1);
            border: 1px solid rgba(72, 206, 29, 0.3);
            border-radius: 15px;
            padding: 25px;
            display: flex;
            align-items: center;
            gap: 20px;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(72, 206, 29, 0.2);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
        }
        
        .stat-info h3 {
            font-size: 2rem;
            color: #48ce1d;
            margin: 0 0 5px 0;
        }
        
        .stat-info p {
            color: #ccc;
            margin: 0;
        }
        
        .chat-admin-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .chat-sessions-list {
            background: rgba(72, 206, 29, 0.05);
            border: 1px solid rgba(72, 206, 29, 0.2);
            border-radius: 15px;
            padding: 25px;
        }
        
        .chat-sessions-list h3 {
            color: #48ce1d;
            margin-bottom: 20px;
        }
        
        .chat-session-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(72, 206, 29, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chat-session-item:hover {
            background: rgba(72, 206, 29, 0.1);
            border-color: #48ce1d;
        }
        
        .chat-session-item.active {
            background: rgba(72, 206, 29, 0.2);
            border-color: #48ce1d;
        }
        
        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .session-user {
            font-weight: bold;
            color: #48ce1d;
        }
        
        .session-time {
            color: #ccc;
            font-size: 0.9rem;
        }
        
        .session-preview {
            color: #ccc;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .chat-details {
            background: rgba(72, 206, 29, 0.05);
            border: 1px solid rgba(72, 206, 29, 0.2);
            border-radius: 15px;
            padding: 25px;
        }
        
        .chat-details h3 {
            color: #48ce1d;
            margin-bottom: 20px;
        }
        
        .chat-messages {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
        }
        
        .chat-message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .chat-message.user {
            background: rgba(72, 206, 29, 0.2);
            margin-left: auto;
            text-align: left;
        }
        
        .chat-message.bot {
            background: rgba(255, 255, 255, 0.1);
            margin-right: auto;
        }
        
        .chat-actions {
            display: flex;
            gap: 15px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 206, 29, 0.3);
        }
        
        .action-btn.secondary {
            background: transparent;
            border: 1px solid #48ce1d;
            color: #48ce1d;
        }
        
        .action-btn.secondary:hover {
            background: #48ce1d;
            color: #000;
        }
        
        @media (max-width: 768px) {
            .chat-admin-content {
                grid-template-columns: 1fr;
            }
            
            .chat-admin-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* أنماط الإشعارات */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .notification {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            font-weight: 600;
        }
        

        

    </style>
</head>
<body>
    <!-- رأس الموقع -->
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="admin.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="teachers.html">المدرسين</a></li>
                <li><a href="students.html">الطلاب</a></li>
                <li><a href="parents.html">أولياء الأمور</a></li>
                <li><a href="invoices.html">الفواتير</a></li>
                <li><a href="sessions.html">الحصص</a></li>
                <li><a href="attendance.html">الحضور</a></li>
                <li><a href="javascript:void(0)" onclick="showChatSection()">المحادثات</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="logout()">تسجيل خروج</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="teachers.html" class="login-btn-links">المدرسين</a></li>
            <li><a href="students.html" class="login-btn-links">الطلاب</a></li>
            <li><a href="parents.html" class="login-btn-links">أولياء الأمور</a></li>
            <li><a href="invoices.html" class="login-btn-links">الفواتير</a></li>
            <li><a href="sessions.html" class="login-btn-links">الحصص</a></li>
            <li><a href="attendance.html" class="login-btn-links">الحضور</a></li>
            <li><a href="javascript:void(0)" class="login-btn-links" onclick="showChatSection()">المحادثات</a></li>
            <li><a href="settings.html" class="login-btn-links">الإعدادات</a></li>
        </ul>
        <div class="header-actions">
            <button class="action-btn login" onclick="logout()">تسجيل خروج</button>
            <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
        </div>
    </div>

    <div class="admin-container">
        <!-- قسم المحادثات -->
        <div id="chatSection" style="display: none;">
            <div class="chat-admin-header">
                <h2>إدارة المحادثات</h2>
                <p>مراقبة وإدارة محادثات الشات بوت</p>
                <div style="margin-top: 15px; display: flex; gap: 10px; justify-content: center;">
                    <button onclick="loadChatData()" class="action-btn">
                        🔄 تحديث البيانات
                    </button>
                    <button onclick="clearAllSessions()" class="action-btn secondary">
                        🗑️ حذف جميع الجلسات
                    </button>
                </div>
            </div>
            
            <div class="chat-admin-stats">
                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-info">
                        <h3 id="totalConversations">0</h3>
                        <p>إجمالي المحادثات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info">
                        <h3 id="activeUsers">0</h3>
                        <p>المستخدمين النشطين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-info">
                        <h3 id="avgResponseTime">0</h3>
                        <p>متوسط وقت الاستجابة (ثانية)</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-info">
                        <h3 id="satisfactionRate">0%</h3>
                        <p>معدل الرضا</p>
                    </div>
                </div>
            </div>
            
            <div class="chat-admin-content">
                <div class="chat-sessions-list">
                    <h3>جلسات المحادثة الحديثة</h3>
                    <div id="chatSessionsList">
                        <!-- سيتم ملء هذا القسم بالبيانات -->
                    </div>
                </div>
                
                <div class="chat-details" id="chatDetails" style="display: none;">
                    <h3>تفاصيل المحادثة</h3>
                    <div class="chat-messages" id="chatMessages">
                        <!-- رسائل المحادثة -->
                    </div>
                    <div class="chat-actions">
                        <button onclick="sendCustomResponse()" class="action-btn">إرسال رد مخصص</button>
                        <button onclick="deleteSession()" class="action-btn secondary" style="background: #ff6b6b;">حذف الجلسة</button>
                        <button onclick="closeChat()" class="action-btn secondary">إغلاق المحادثة</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const isAdminLoggedIn = localStorage.getItem('adminLoggedIn');
            if (!isAdminLoggedIn || isAdminLoggedIn !== 'true') {
                alert('يجب تسجيل الدخول أولاً');
                window.location.href = 'login.html';
                return;
            }
        });

        // دالة تسجيل الخروج المباشر
        function logout() {
            localStorage.removeItem('adminLoggedIn');
            localStorage.removeItem('adminUsername');
            window.location.href = 'login.html';
        }

        // دالة القائمة المحمولة
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }

        // دالة عرض قسم المحادثات
        function showChatSection() {
            document.getElementById('chatSection').style.display = 'block';
            loadChatData();
            
            // تحديث البيانات كل 5 ثوان
            setInterval(() => {
                if (document.getElementById('chatSection').style.display !== 'none') {
                    loadChatData();
                }
            }, 5000);
            
            // مراقبة الرسائل الجديدة
            checkForNewMessages();
        }
        
        // دالة مراقبة الرسائل الجديدة
        function checkForNewMessages() {
            let lastMessageCount = 0;
            
            setInterval(() => {
                const sessions = JSON.parse(localStorage.getItem('adminChatSessions') || '[]');
                const currentMessageCount = sessions.length;
                
                if (currentMessageCount > lastMessageCount && lastMessageCount > 0) {
                    // رسالة جديدة وصلت
                    showNotification('رسالة جديدة وصلت!', 'success');
                }
                
                lastMessageCount = currentMessageCount;
            }, 3000);
        }
        
        // دالة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#48ce1d' : '#ff6b6b'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // دالة تحميل بيانات المحادثات
        function loadChatData() {
            // تحميل الإحصائيات
            loadChatStats();
            // تحميل جلسات المحادثة
            loadChatSessions();
        }

        // دالة تحميل الإحصائيات
        function loadChatStats() {
            // قراءة الجلسات من localStorage
            const sessions = JSON.parse(localStorage.getItem('adminChatSessions') || '[]');
            updateStats(sessions);
        }
        
        // دالة تحديث الإحصائيات
        function updateStats(sessions) {
            const totalConversations = sessions.length;
            const activeUsers = new Set(sessions.map(s => s.user)).size;
            
            // حساب متوسط وقت الاستجابة (وهمي)
            const avgResponseTime = (2.3 + Math.random()).toFixed(1);
            
            // حساب معدل الرضا (وهمي)
            const satisfactionRate = Math.floor(85 + Math.random() * 15);
            
            document.getElementById('totalConversations').textContent = totalConversations;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('avgResponseTime').textContent = avgResponseTime;
            document.getElementById('satisfactionRate').textContent = satisfactionRate + '%';
        }

        // دالة تحميل جلسات المحادثة
        function loadChatSessions() {
            const sessionsList = document.getElementById('chatSessionsList');
            
            // قراءة الجلسات من localStorage
            let sessions = JSON.parse(localStorage.getItem('adminChatSessions') || '[]');
            
            // إذا لم تكن هناك جلسات، استخدم بيانات وهمية
            if (sessions.length === 0) {
                sessions = [
                    {
                        id: 1,
                        user: 'أحمد محمد',
                        time: 'منذ 5 دقائق',
                        preview: 'أريد معرفة الدورات المتاحة للقرآن الكريم',
                        messages: [
                            { sender: 'user', text: 'أريد معرفة الدورات المتاحة للقرآن الكريم' },
                            { sender: 'bot', text: 'لدينا مجموعة متنوعة من دورات القرآن الكريم...' }
                        ]
                    },
                    {
                        id: 2,
                        user: 'فاطمة علي',
                        time: 'منذ 15 دقيقة',
                        preview: 'كيف يمكنني التسجيل في دورة اللغة العربية؟',
                        messages: [
                            { sender: 'user', text: 'كيف يمكنني التسجيل في دورة اللغة العربية؟' },
                            { sender: 'bot', text: 'يمكنك التسجيل في الدورات بعدة طرق...' }
                        ]
                    },
                    {
                        id: 3,
                        user: 'محمد عبدالله',
                        time: 'منذ ساعة',
                        preview: 'ما هي أسعار الدورات المتقدمة؟',
                        messages: [
                            { sender: 'user', text: 'ما هي أسعار الدورات المتقدمة؟' },
                            { sender: 'bot', text: 'أسعار الدورات المتقدمة تتراوح بين 500-1000 ريال...' }
                        ]
                    }
                ];
            }

            sessionsList.innerHTML = '';
            
            // ترتيب الجلسات من الأحدث إلى الأقدم
            sessions.sort((a, b) => new Date(b.timestamp || b.id) - new Date(a.timestamp || a.id));
            
            sessions.forEach(session => {
                const sessionItem = document.createElement('div');
                sessionItem.className = 'chat-session-item';
                sessionItem.onclick = () => showChatDetails(session);
                
                // تنسيق الوقت
                let timeDisplay = session.time;
                if (session.timestamp) {
                    const now = new Date();
                    const sessionTime = new Date(session.timestamp);
                    const diffMinutes = Math.floor((now - sessionTime) / (1000 * 60));
                    
                    if (diffMinutes < 1) {
                        timeDisplay = 'الآن';
                    } else if (diffMinutes < 60) {
                        timeDisplay = `منذ ${diffMinutes} دقيقة`;
                    } else if (diffMinutes < 1440) {
                        const hours = Math.floor(diffMinutes / 60);
                        timeDisplay = `منذ ${hours} ساعة`;
                    } else {
                        timeDisplay = sessionTime.toLocaleDateString('ar-SA');
                    }
                }
                
                sessionItem.innerHTML = `
                    <div class="session-header">
                        <span class="session-user">${session.user}</span>
                        <span class="session-time">${timeDisplay}</span>
                    </div>
                    <div class="session-preview">${session.preview}</div>
                `;
                
                sessionsList.appendChild(sessionItem);
            });
            
            // تحديث الإحصائيات بناءً على الجلسات الفعلية
            updateStats(sessions);
        }

        // دالة عرض تفاصيل المحادثة
        function showChatDetails(session) {
            // إزالة التحديد من جميع العناصر
            document.querySelectorAll('.chat-session-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // تحديد العنصر المحدد
            event.target.closest('.chat-session-item').classList.add('active');
            
            // عرض تفاصيل المحادثة
            const chatDetails = document.getElementById('chatDetails');
            const chatMessages = document.getElementById('chatMessages');
            
            chatMessages.innerHTML = '';
            
            session.messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `chat-message ${message.sender}`;
                messageDiv.textContent = message.text;
                chatMessages.appendChild(messageDiv);
            });
            
            chatDetails.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // دالة إرسال رد مخصص
        function sendCustomResponse() {
            const response = prompt('أدخل الرد المخصص:');
            if (response && response.trim()) {
                const chatMessages = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'chat-message bot';
                messageDiv.textContent = response;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        // دالة إغلاق المحادثة
        function closeChat() {
            document.getElementById('chatDetails').style.display = 'none';
            document.querySelectorAll('.chat-session-item').forEach(item => {
                item.classList.remove('active');
            });
        }
        
        // دالة حذف جميع الجلسات
        function clearAllSessions() {
            if (confirm('هل أنت متأكد من حذف جميع جلسات المحادثة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.removeItem('adminChatSessions');
                localStorage.removeItem('chatMessagesForAdmin');
                
                // إعادة تحميل البيانات
                loadChatData();
                
                showNotification('تم حذف جميع الجلسات بنجاح', 'success');
            }
        }
        
        // دالة حذف الجلسة
        function deleteSession() {
            if (confirm('هل أنت متأكد من حذف هذه الجلسة؟')) {
                const activeSession = document.querySelector('.chat-session-item.active');
                if (activeSession) {
                    // حذف الجلسة من localStorage
                    const sessions = JSON.parse(localStorage.getItem('adminChatSessions') || '[]');
                    const sessionIndex = Array.from(document.querySelectorAll('.chat-session-item')).indexOf(activeSession);
                    
                    if (sessionIndex !== -1) {
                        sessions.splice(sessionIndex, 1);
                        localStorage.setItem('adminChatSessions', JSON.stringify(sessions));
                        
                        // إزالة العنصر من الواجهة
                        activeSession.remove();
                        
                        // إغلاق تفاصيل المحادثة
                        closeChat();
                        
                        // تحديث الإحصائيات
                        updateStats(sessions);
                        
                        showNotification('تم حذف الجلسة بنجاح', 'success');
                    }
                }
            }
        }

    </script>
</body>
</html>
