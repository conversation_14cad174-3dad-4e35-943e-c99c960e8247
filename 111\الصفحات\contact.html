<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - منصة دارين التعليمية</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* تخصيص صفحة الاتصال */
        .contact-hero-section {
            background: linear-gradient(135deg, #48ce1d 0%, #3eff00 50%, #48ce1d 100%);
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .contact-hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .contact-hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 32px;
            position: relative;
            z-index: 2;
        }

        .contact-hero-title {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 3.5rem;
            font-weight: 900;
            color: #000;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .contact-hero-desc {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 1.2rem;
            color: #000;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .contact-content-section {
            padding: 80px 0;
            background: #151413;
        }

        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 32px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: stretch;
        }

        .contact-info {
            background: #191717;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(72, 206, 29, 0.2);
            height: fit-content;
        }

        .contact-info-title {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 2rem;
            font-weight: 700;
            color: #48ce1d;
            margin-bottom: 30px;
            text-align: center;
        }

        .contact-methods {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: rgba(72, 206, 29, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(72, 206, 29, 0.2);
            transition: all 0.3s ease;
        }

        .contact-method:hover {
            background: rgba(72, 206, 29, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 206, 29, 0.2);
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: #48ce1d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .contact-icon svg {
            width: 24px;
            height: 24px;
            fill: #000;
        }

        .contact-details h4 {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 1.1rem;
            font-weight: 600;
            color: #fff;
            margin: 0 0 5px 0;
        }

        .contact-details p {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 0.95rem;
            color: #ccc;
            margin: 0;
            line-height: 1.4;
        }

        .contact-details a {
            color: #48ce1d;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-details a:hover {
            color: #3eff00;
        }

        .contact-form {
            background: #191717;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(72, 206, 29, 0.2);
            height: fit-content;
        }

        .contact-form-title {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 2rem;
            font-weight: 700;
            color: #48ce1d;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 0.95rem;
            font-weight: 600;
            color: #fff;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            background: #151413;
            border: 2px solid rgba(72, 206, 29, 0.3);
            border-radius: 12px;
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 1rem;
            color: #fff;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #48ce1d;
            box-shadow: 0 0 0 3px rgba(72, 206, 29, 0.1);
        }

        .form-input::placeholder {
            color: #666;
        }

        textarea.form-input {
            resize: vertical;
            min-height: 120px;
        }

        .submit-btn {
            width: 100%;
            padding: 18px 30px;
            background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
            border: none;
            border-radius: 12px;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 1.1rem;
            font-weight: 700;
            color: #000;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(72, 206, 29, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 206, 29, 0.4);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .map-section {
            padding: 60px 0;
            background: #191717;
        }

        .map-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 32px;
        }

        .map-title {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #48ce1d;
            text-align: center;
            margin-bottom: 40px;
        }

        .map-wrapper {
            background: #151413;
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(72, 206, 29, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .map-placeholder {
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .social-contact-section {
            padding: 60px 0;
            background: #151413;
        }

        .social-contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 32px;
            text-align: center;
        }

        .social-contact-title {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #48ce1d;
            margin-bottom: 20px;
        }

        .social-contact-desc {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 1.1rem;
            color: #ccc;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .social-contact-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            max-width: 1000px;
            margin: 0 auto;
        }

        .social-contact-card {
            background: linear-gradient(180deg, rgba(25,23,23,0.95) 0%, rgba(21,20,19,0.95) 100%);
            border-radius: 16px;
            padding: 30px 20px;
            border: 1px solid rgba(72, 206, 29, 0.2);
            transition: transform .25s ease, box-shadow .25s ease, border-color .25s ease;
            text-decoration: none;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .social-contact-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 12px 34px rgba(72, 206, 29, 0.25);
            border-color: #48ce1d;
        }
        
        .social-contact-card[onclick] {
            cursor: pointer;
        }

        .social-contact-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            background: #48ce1d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .social-contact-icon img {
            width: 30px;
            height: 30px;
        }

        .social-contact-icon svg {
            width: 30px;
            height: 30px;
            fill: #000;
            display: block;
        }

        .social-contact-name {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 1.1rem;
            font-weight: 600;
            color: #fff;
            margin-bottom: 8px;
        }

        .social-contact-handle {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 0.9rem;
            color: #48ce1d;
        }

        @media (max-width: 900px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .contact-hero-title {
                font-size: 2.8rem;
            }

            .map-title,
            .social-contact-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 700px) {
            .contact-hero-section {
                padding: 60px 0;
            }

            .contact-hero-title {
                font-size: 2.2rem;
            }

            .contact-hero-desc {
                font-size: 1rem;
            }

            .contact-content-section {
                padding: 60px 0;
            }

            .contact-info,
            .contact-form {
                padding: 30px 20px;
            }

            .contact-info-title,
            .contact-form-title {
                font-size: 1.6rem;
            }

            .contact-method {
                padding: 15px;
            }

            .contact-icon {
                width: 40px;
                height: 40px;
            }

            .contact-icon svg {
                width: 20px;
                height: 20px;
            }

            .map-section,
            .social-contact-section {
                padding: 40px 0;
            }

            .map-placeholder {
                height: 300px;
                font-size: 1rem;
            }

            .social-contact-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .social-contact-card {
                padding: 25px 15px;
            }
        }

        /* مسار الصفحة (Breadcrumb) */
        .contact-breadcrumb {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: rgba(0,0,0,0.08);
            border-radius: 999px;
            padding: 8px 14px;
            margin-top: 16px;
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            font-size: 0.95rem;
            color: #000;
        }
        .contact-breadcrumb a {
            color: #000;
            text-decoration: none;
            opacity: 0.9;
        }
        .contact-breadcrumb .sep { opacity: 0.6; }

        /* رسائل أخطاء الحقول */
        .field-error {
            margin-top: 8px;
            color: #ff6b6b;
            font-size: 0.85rem;
            display: none;
        }
        .form-input.input-error {
            border-color: #ff6b6b !important;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }

        /* Toast */
        .toast {
            position: fixed;
            bottom: 24px;
            left: 50%;
            transform: translateX(-50%) translateY(20px);
            background: #191717;
            color: #fff;
            border: 1px solid rgba(72, 206, 29, 0.3);
            box-shadow: 0 10px 30px rgba(0,0,0,0.25);
            border-radius: 12px;
            padding: 12px 18px;
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0;
            pointer-events: none;
            transition: opacity .25s ease, transform .25s ease;
            z-index: 1100;
            font-family: 'Cairo', Arial, sans-serif;
        }
        .toast.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
            pointer-events: auto;
        }
        .toast .dot {
            width: 10px; height: 10px; border-radius: 50%; background: #48ce1d;
        }

        /* عناصر مخفية بصرياً لسهولة الوصول (لـ honeypot وغيره) */
        .visually-hidden {
            position: absolute !important;
            width: 1px; height: 1px;
            padding: 0; margin: -1px;
            overflow: hidden; clip: rect(0, 0, 0, 0);
            white-space: nowrap; border: 0;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="index.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="courses.html">الدورات</a></li>
                <li><a href="contact.html">اتصل بنا</a></li>
                <li><a href="about.html">من نحن</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="index.html" class="login-btn-links">الرئيسية</a></li>
            <li><a href="courses.html" class="login-btn-links">الدورات</a></li>
            <li><a href="contact.html" class="login-btn-links">اتصل بنا</a></li>
            <li><a href="about.html" class="login-btn-links">من نحن</a></li>
        </ul>
        <div class="header-actions">
            <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
            <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
        </div>
    </div>
    
    <!-- Hero Section -->
    <section class="contact-hero-section">
        <div class="contact-hero-container">
            <h1 class="contact-hero-title">اتصل بنا</h1>
            <p class="contact-hero-desc">نسعد بخدمتك والرد على استفساراتك في أقرب وقت. تواصل معنا الآن.</p>
            <nav class="contact-breadcrumb" aria-label="مسار الصفحة">
                <a href="index.html">الرئيسية</a>
                <span class="sep">/</span>
                <span aria-current="page">اتصل بنا</span>
            </nav>
        </div>
    </section>

    <!-- Contact Content -->
    <section class="contact-content-section">
        <div class="contact-container">
            <div class="contact-grid">
                <!-- معلومات الاتصال -->
                <div class="contact-info">
                    <h2 class="contact-info-title">معلومات التواصل</h2>
                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h4>البريد الإلكتروني</h4>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>

                        <div class="contact-method">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h4>الهاتف</h4>
                                <p><a href="tel:+966501234567">+966 50 123 4567</a></p>
                            </div>
                        </div>

                        <div class="contact-method">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h4>العنوان</h4>
                                <p>الرياض، المملكة العربية السعودية<br>شارع الملك فهد، برج دارين التعليمي</p>
                            </div>
                        </div>

                        <div class="contact-method">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h4>ساعات العمل</h4>
                                <p>الأحد - الخميس: 8:00 ص - 6:00 م<br>الجمعة - السبت: 10:00 ص - 4:00 م</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج الاتصال -->
                <div class="contact-form">
                    <h2 class="contact-form-title">أرسل رسالة</h2>
                    <form id="contactForm">
                        <div class="form-group">
                            <label for="name">الاسم الكامل</label>
                            <input type="text" id="name" name="name" class="form-input" placeholder="أدخل اسمك الكامل" required aria-required="true" aria-describedby="nameError" autocomplete="name">
                            <div class="field-error" id="nameError">من فضلك أدخل الاسم الكامل.</div>
                        </div>

                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email" class="form-input" placeholder="أدخل بريدك الإلكتروني" required aria-required="true" aria-describedby="emailError" autocomplete="email" inputmode="email" dir="ltr">
                            <div class="field-error" id="emailError">يرجى إدخال بريد إلكتروني صالح.</div>
                        </div>

                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" class="form-input" placeholder="أدخل رقم هاتفك" pattern="^\+?\d{7,15}$" aria-describedby="phoneError" autocomplete="tel" inputmode="tel" dir="ltr">
                            <div class="field-error" id="phoneError">رجاءً أدخل رقم هاتف صحيح (7-15 رقم).</div>
                        </div>

                        <div class="form-group">
                            <label for="subject">الموضوع</label>
                            <select id="subject" name="subject" class="form-input" required aria-required="true" aria-describedby="subjectError">
                                <option value="">اختر الموضوع</option>
                                <option value="general">استفسار عام</option>
                                <option value="courses">استفسار عن الدورات</option>
                                <option value="technical">مشكلة تقنية</option>
                                <option value="support">طلب دعم</option>
                                <option value="other">أخرى</option>
                            </select>
                            <div class="field-error" id="subjectError">يرجى اختيار موضوع الرسالة.</div>
                        </div>

                        <div class="form-group">
                            <label for="message">الرسالة</label>
                            <textarea id="message" name="message" class="form-input" placeholder="اكتب رسالتك هنا..." required aria-required="true" aria-describedby="messageError" minlength="10"></textarea>
                            <div class="field-error" id="messageError">يرجى كتابة رسالة لا تقل عن 10 أحرف.</div>
                        </div>

                        <!-- حقل honeypot لمنع السبام -->
                        <div class="visually-hidden" aria-hidden="true">
                            <label for="hp-company">لا تملأ هذا الحقل</label>
                            <input type="text" id="hp-company" name="company" tabindex="-1" autocomplete="off">
                        </div>

                        <button type="submit" class="submit-btn" id="submitBtn" aria-label="إرسال الرسالة">إرسال الرسالة</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
        <div class="map-container">
            <h2 class="map-title">موقعنا</h2>
            <div class="map-wrapper">
                <iframe title="خريطة موقع معهد دارين" aria-label="خريطة" src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d3624.*********!2d46.6753!3d24.7136!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjTCsDQyJzQ5LjAiTiA0NsKwNDAnMzEuMCJF!5e0!3m2!1sar!2ssa!4v*********0" width="100%" height="400" style="border:0; border-radius:12px;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
        </div>
    </section>

    <!-- Social Contact Section -->
    <section class="social-contact-section">
        <div class="social-contact-container">
            <h2 class="social-contact-title">تواصل معنا عبر وسائل التواصل الاجتماعي</h2>
            <p class="social-contact-desc">تابعنا على منصات التواصل الاجتماعي للحصول على آخر الأخبار والتحديثات</p>
            
            <div class="social-contact-grid">
                <a href="#" class="social-contact-card" target="_blank" rel="noopener">
                    <div class="social-contact-icon">
                        <img src="../assets/images/instagram.png" alt="Instagram">
                    </div>
                    <div class="social-contact-name">إنستغرام</div>
                    <div class="social-contact-handle">@dareen_edu</div>
                </a>

                <a href="#" class="social-contact-card" target="_blank" rel="noopener">
                    <div class="social-contact-icon">
                        <img src="../assets/images/telegram.png" alt="Telegram">
                    </div>
                    <div class="social-contact-name">تليجرام</div>
                    <div class="social-contact-handle">@dareen_channel</div>
                </a>

                <a href="#" class="social-contact-card" target="_blank" rel="noopener">
                    <div class="social-contact-icon">
                        <img src="../assets/images/tiktok.png" alt="TikTok">
                    </div>
                    <div class="social-contact-name">تيك توك</div>
                    <div class="social-contact-handle">@dareen_tiktok</div>
                </a>

                <a href="https://wa.me/966501234567" class="social-contact-card" target="_blank" rel="noopener">
                    <div class="social-contact-icon">
                        <!-- WhatsApp SVG Icon -->
                        <svg viewBox="0 0 24 24" aria-label="WhatsApp" role="img">
                            <path d="M20.52 3.48A11.86 11.86 0 0 0 12.05 0C5.46 0 .12 5.34.12 11.93c0 2.1.55 4.16 1.6 5.98L0 24l6.26-1.64a12 12 0 0 0 5.79 1.48h.01c6.59 0 11.93-5.34 11.93-11.93 0-3.19-1.24-6.19-3.47-8.43zM12.06 21.9h-.01a9.9 9.9 0 0 1-5.04-1.39l-.36-.21-3.72.97.99-3.63-.24-.37a9.9 9.9 0 1 1 8.38 4.63zM17.2 14.31c-.3-.15-1.76-.87-2.03-.97-.27-.1-.46-.15-.65.15-.2.3-.75.97-.92 1.17-.17.2-.34.23-.64.08-.3-.15-1.26-.46-2.4-1.47-.89-.79-1.49-1.77-1.66-2.07-.17-.3-.02-.46.13-.61.13-.13.3-.34.45-.51.15-.17.2-.3.3-.5.1-.2.05-.38-.02-.53-.08-.15-.65-1.56-.9-2.14-.24-.58-.48-.49-.65-.5l-.56-.01c-.2 0-.53.08-.81.38-.28.3-1.07 1.05-1.07 2.56s1.1 2.96 1.25 3.16c.15.2 2.16 3.3 5.22 ********** 1.3.52 **********.23 1.4.2 **********-.09 1.76-.72 2.01-1.41.25-.69.25-1.28.17-1.41-.07-.13-.26-.2-.56-.35z"/>
                        </svg>
                    </div>
                    <div class="social-contact-name">واتساب</div>
                    <div class="social-contact-handle">+966 50 123 4567</div>
                </a>

            </div>
        </div>
    </section>


    
    <script>
        // دالة القائمة المتحركة
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }

        // مساعدة بسيطة لعرض/إخفاء أخطاء الحقول
        function setFieldError(inputEl, errorEl, hasError, message) {
            if (!errorEl) return;
            if (hasError) {
                inputEl.classList.add('input-error');
                errorEl.textContent = message;
                errorEl.style.display = 'block';
                inputEl.setAttribute('aria-invalid', 'true');
            } else {
                inputEl.classList.remove('input-error');
                errorEl.style.display = 'none';
                inputEl.removeAttribute('aria-invalid');
            }
        }

        function showToast(text) {
            const toast = document.getElementById('toast');
            if (!toast) return alert(text);
            toast.querySelector('.toast-text').textContent = text;
            toast.classList.add('show');
            setTimeout(() => toast.classList.remove('show'), 3500);
        }

        // معالجة نموذج الاتصال مع تحقق واجهة + حالة تحميل + Honeypot
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const nameEl = document.getElementById('name');
            const emailEl = document.getElementById('email');
            const phoneEl = document.getElementById('phone');
            const subjectEl = document.getElementById('subject');
            const messageEl = document.getElementById('message');
            const hpEl = document.getElementById('hp-company');
            const submitBtn = document.getElementById('submitBtn');

            let valid = true;

            // الاسم
            setFieldError(nameEl, document.getElementById('nameError'), !nameEl.value.trim(), 'من فضلك أدخل الاسم الكامل.');
            if (!nameEl.value.trim()) valid = false;

            // البريد
            const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailEl.value.trim());
            setFieldError(emailEl, document.getElementById('emailError'), !emailValid, 'يرجى إدخال بريد إلكتروني صالح.');
            if (!emailValid) valid = false;

            // الهاتف (اختياري)
            if (phoneEl.value.trim()) {
                const phoneValid = /^\+?\d{7,15}$/.test(phoneEl.value.trim());
                setFieldError(phoneEl, document.getElementById('phoneError'), !phoneValid, 'رجاءً أدخل رقم هاتف صحيح (7-15 رقم).');
                if (!phoneValid) valid = false;
            } else {
                setFieldError(phoneEl, document.getElementById('phoneError'), false, '');
            }

            // الموضوع
            setFieldError(subjectEl, document.getElementById('subjectError'), !subjectEl.value, 'يرجى اختيار موضوع الرسالة.');
            if (!subjectEl.value) valid = false;

            // الرسالة
            setFieldError(messageEl, document.getElementById('messageError'), messageEl.value.trim().length < 10, 'يرجى كتابة رسالة لا تقل عن 10 أحرف.');
            if (messageEl.value.trim().length < 10) valid = false;

            if (!valid) {
                showToast('يرجى تصحيح الأخطاء أولاً.');
                return;
            }

            // منع السبام عبر honeypot
            if (hpEl && hpEl.value.trim()) {
                // لا ترسل أي شيء إذا تم ملء الحقل المخفي
                return;
            }

            // حالة تحميل للزر
            submitBtn.disabled = true;
            submitBtn.setAttribute('aria-busy', 'true');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'جارٍ الإرسال...';

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // TODO: إرسال إلى الخادم عبر fetch
            console.log('بيانات النموذج:', data);

            // محاكاة إرسال لمدة قصيرة
            setTimeout(() => {
                showToast('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                this.reset();
                submitBtn.disabled = false;
                submitBtn.removeAttribute('aria-busy');
                submitBtn.textContent = originalText;
            }, 1200);
        });

        // تحسين تجربة المستخدم للنموذج
        const inputs = document.querySelectorAll('.form-input');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });

            // تصحيح الخطأ مباشرة أثناء الكتابة
            input.addEventListener('input', function() {
                const id = this.id;
                if (!id) return;
                const errorEl = document.getElementById(id + 'Error');
                if (!errorEl) return;

                let hasError = false;
                if (id === 'email') {
                    const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.value.trim());
                    hasError = !emailValid;
                } else if (id === 'phone') {
                    if (this.value.trim()) {
                        hasError = !/^\+?\d{7,15}$/.test(this.value.trim());
                    } else {
                        hasError = false;
                    }
                } else if (id === 'name') {
                    hasError = this.value.trim().length === 0;
                } else if (id === 'message') {
                    hasError = this.value.trim().length < 10;
                }

                setFieldError(this, errorEl, hasError, errorEl.textContent || '');
            });
        });
        
        
</script>
    <!-- Toast -->
    <div class="toast" id="toast" role="status" aria-live="polite">
        <span class="dot"></span>
        <span class="toast-text">تم الإرسال بنجاح</span>
    </div>
</body>
</html>