<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/style.css">
    <title>الدورات - دارين لتعليم</title>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="index.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="courses.html">الدورات</a></li>
                <li><a href="contact.html">اتصل بنا</a></li>
                <li><a href="about.html">من نحن</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="index.html" class="login-btn-links">الرئيسية</a></li>
            <li><a href="courses.html" class="login-btn-links">الدورات</a></li>
            <li><a href="contact.html" class="login-btn-links">اتصل بنا</a></li>
            <li><a href="about.html" class="login-btn-links">من نحن</a></li>
        </ul>
        <div class="header-actions">
            <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
            <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
        </div>
    </div>
    
    <main class="courses-main" style="padding: 50px 20px; min-height: 60vh; background: #151413; color: white; text-align: center;">
      <section class="courses-hero-section" style="display: flex; flex-wrap: wrap; align-items: center; justify-content: center; gap: 40px; max-width: 1100px; margin: 0 auto 10px auto;">
        <!-- الجزء النصي -->
        <div style="flex: 1 1 320px; min-width: 260px; max-width: 500px; text-align: right;">
          <h2 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 900; font-size: 2.1rem; margin-bottom: 18px; text-align: center;">مرحبًا بك في منصة الدورات</h2>
          <p style="font-size: 1.13rem; font-family: 'Noto Kufi Arabic', Arial, sans-serif; color: #fff; opacity: 0.92; line-height: 1.7; text-align: center;">
            هنا تجد جميع الدورات التعليمية لمراحل التعليم المختلفة، بالإضافة إلى دورات إضافية ودورات القرآن الكريم والقدرات.<br>اختر القسم المناسب وابدأ رحلتك التعليمية معنا!
          </p>
          <!-- حقل البحث وزرين -->
          <div class="search-bar-container" style="max-width: 800px; margin: 18px auto 0 auto; display: flex; flex-direction: column; gap: 8px; justify-content: center; align-items: center;">
            <input type="text" placeholder="ابحث عن دورة..." style="width: 100%; max-width: 600px; padding: 16px 22px; border-radius: 24px; border: none; font-size: 1.15rem; font-family: 'Noto Kufi Arabic', Arial, sans-serif; outline: none; background: #232323; color: #fff; box-shadow: 0 2px 8px #0002; margin-bottom: 0;" />
            <div style="display: flex; gap: 10px; justify-content: center; width: 100%;">
              <button style="padding: 15px 34px; border-radius: 24px; border: none; background: linear-gradient(90deg, #48ce1d 60%, #3eff00 100%); color: #191717; font-size: 1.13rem; font-family: 'Cairo', Arial, sans-serif; font-weight: bold; cursor: pointer; transition: background 0.2s;">بحث</button>
              <button style="padding: 15px 26px; border-radius: 24px; border: 2px solid #48ce1d; background: transparent; color: #48ce1d; font-size: 1.13rem; font-family: 'Cairo', Arial, sans-serif; font-weight: bold; cursor: pointer; transition: background 0.2s, color 0.2s;">طلب المساعدة</button>
            </div>
          </div>
        </div>
        <!-- الجزء الخاص بالصورة -->
        <div class="hero-img-col hide-on-mobile" style="flex: 1 1 320px; min-width: 260px; max-width: 420px; display: flex; align-items: center; justify-content: center;">
          <img src="../assets/images/cour2.png" alt="دورات تعليمية" style="width: 100%; max-width: 500px; margin-bottom: 8px;" />
        </div>
      </section>
      <!-- المراحل الدراسية (مطابق للرئيسية) -->
      <section class="foundation-courses-section why-dareen-section">
        <div class="foundation-courses-container why-dareen-container">
          <h2 class="foundation-courses-title why-dareen-title">المراحل الدراسية</h2>
          <div class="foundation-courses-grid why-dareen-grid">
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-arabic.png" alt="المرحلة الابتدائية">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">المرحلة الابتدائية</div>
              <div class="foundation-course-details why-dareen-card-desc">12 درس - تأسيس القراءة والكتابة وقواعد النحو</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">4.0</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-english.png" alt="المرحلة المتوسطة">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">المرحلة المتوسطة</div>
              <div class="foundation-course-details why-dareen-card-desc">10 دروس - تأسيس القواعد والمحادثة والاستماع</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">5.0</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-math.png" alt="المرحلة الثانوية">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">المرحلة الثانوية</div>
              <div class="foundation-course-details why-dareen-card-desc">15 درس - أساسيات الحساب والهندسة والتفكير المنطقي</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">4.5</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
          </div>
        </div>
      </section>
      <!-- نسخة ثانية من كورسات التأسيس -->
      <section class="foundation-courses-section why-dareen-section">
        <div class="foundation-courses-container why-dareen-container">
          <h2 class="foundation-courses-title why-dareen-title">كورسات التأسيس</h2>
          <div class="foundation-courses-grid why-dareen-grid">
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-arabic.png" alt="اللغة العربية">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">اللغة العربية</div>
              <div class="foundation-course-details why-dareen-card-desc">12 درس - تأسيس القراءة والكتابة وقواعد النحو</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">4.0</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-english.png" alt="اللغة الإنجليزية">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">اللغة الإنجليزية</div>
              <div class="foundation-course-details why-dareen-card-desc">10 دروس - تأسيس القواعد والمحادثة والاستماع</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">5.0</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-math.png" alt="الرياضيات">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">الرياضيات</div>
              <div class="foundation-course-details why-dareen-card-desc">15 درس - أساسيات الحساب والهندسة والتفكير المنطقي</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">4.5</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
          </div>
        </div>
      </section>
      <!-- نسخة ثالثة من كورسات التأسيس -->
      <section class="foundation-courses-section why-dareen-section">
        <div class="foundation-courses-container why-dareen-container">
          <h2 class="foundation-courses-title why-dareen-title">كورسات أضافية</h2>
          <div class="foundation-courses-grid why-dareen-grid">
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-arabic.png" alt="اللغة العربية">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">اللغة العربية</div>
              <div class="foundation-course-details why-dareen-card-desc">12 درس - تأسيس القراءة والكتابة وقواعد النحو</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">4.0</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-english.png" alt="اللغة الإنجليزية">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">اللغة الإنجليزية</div>
              <div class="foundation-course-details why-dareen-card-desc">10 دروس - تأسيس القواعد والمحادثة والاستماع</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">5.0</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
            <div class="foundation-course-card why-dareen-card">
              <div class="foundation-course-img">
                <img src="../assets/images/foundation-math.png" alt="الرياضيات">
                <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
              </div>
              <div class="foundation-course-name why-dareen-card-title">الرياضيات</div>
              <div class="foundation-course-details why-dareen-card-desc">15 درس - أساسيات الحساب والهندسة والتفكير المنطقي</div>
              <div class="foundation-rating-box">
                <span class="foundation-rating-type">مدفوعة</span>
                <span class="foundation-rating-value">4.5</span>
                <span class="foundation-rating-stars">
                  <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                </span>
              </div>
              <button class="foundation-course-btn">عرض التفاصيل</button>
            </div>
          </div>
        </div>
      </section>
      <!-- يمكنك البدء في إضافة محتوى جديد هنا -->
    </main>
    
    <script>
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }
    </script>
    <script>
window.addEventListener('scroll', function() {
  var btn = document.querySelector('.back-to-top');
  if (window.scrollY > 200) {
    btn.classList.add('show');
  } else {
    btn.classList.remove('show');
  }
});
document.querySelector('.back-to-top').onclick = function() {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// وظائف البحث
function searchCourses() {
    const searchInput = document.querySelector('input[placeholder="ابحث عن دورة..."]');
    const searchTerm = searchInput.value.trim().toLowerCase();

    if (searchTerm === '') {
        alert('يرجى إدخال كلمة للبحث');
        return;
    }

    // البحث في جميع كروت الدورات
    const courseCards = document.querySelectorAll('.foundation-course-card');
    let foundResults = false;

    courseCards.forEach(card => {
        const courseName = card.querySelector('.foundation-course-name')?.textContent.toLowerCase() || '';
        const courseDetails = card.querySelector('.foundation-course-details')?.textContent.toLowerCase() || '';

        if (courseName.includes(searchTerm) || courseDetails.includes(searchTerm)) {
            card.style.display = 'block';
            card.style.border = '2px solid #48ce1d';
            card.style.boxShadow = '0 4px 15px rgba(72, 206, 29, 0.3)';
            foundResults = true;
        } else {
            card.style.display = 'none';
        }
    });

    if (!foundResults) {
        alert('لم يتم العثور على دورات تحتوي على: ' + searchTerm);
        resetSearch();
    }
}

function resetSearch() {
    const courseCards = document.querySelectorAll('.foundation-course-card');
    courseCards.forEach(card => {
        card.style.display = 'block';
        card.style.border = '';
        card.style.boxShadow = '';
    });
}

function requestHelp() {
    alert('سيتم التواصل معك قريباً! يمكنك أيضاً التواصل معنا عبر:\n\n📞 الهاتف: +965 1234 5678\n📧 البريد: <EMAIL>\n💬 واتساب: +965 9876 5432');
}

// ربط الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[placeholder="ابحث عن دورة..."]');
    const searchButton = document.querySelector('button:contains("بحث")');
    const helpButton = document.querySelector('button:contains("طلب المساعدة")');

    // البحث عند الضغط على زر البحث
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        if (button.textContent.includes('بحث')) {
            button.onclick = searchCourses;
        }
        if (button.textContent.includes('طلب المساعدة')) {
            button.onclick = requestHelp;
        }
    });

    // البحث عند الضغط على Enter
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCourses();
            }
        });

        // إعادة تعيين البحث عند مسح النص
        searchInput.addEventListener('input', function() {
            if (this.value.trim() === '') {
                resetSearch();
            }
        });
    }
});
    </script>
    <button class="back-to-top" title="الصعود للأعلى">
      <svg width="22" height="22" viewBox="0 0 24 24" fill="#48ce1d" xmlns="http://www.w3.org/2000/svg">
        <polygon points="12,6 18,16 6,16" />
      </svg>
    </button>
  </body>
</html>
