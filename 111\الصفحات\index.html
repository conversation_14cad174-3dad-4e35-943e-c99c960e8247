<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/style.css">
    <style>
      /* بطاقة التسجيل المحسنة */
      .register-cta-section {
        margin: 40px auto;
        padding: 0 20px;
        max-width: 900px;
      }

      .register-card {
        position: relative;
        background: linear-gradient(135deg, #48ce1d 0%, #3eff00 50%, #48ce1d 100%);
        border-radius: 18px;
        padding: 35px 30px;
        overflow: hidden;
        box-shadow:
          0 15px 40px rgba(72, 206, 29, 0.3),
          0 0 0 1px rgba(255, 255, 255, 0.1);
        transition: all 0.4s ease;
      }

      .register-card:hover {
        transform: translateY(-5px);
        box-shadow:
          0 30px 80px rgba(72, 206, 29, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2);
      }

      .card-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
        pointer-events: none;
      }

      .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
      }

      .floating-icon {
        position: absolute;
        font-size: 2rem;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
      }

      .floating-icon:nth-child(1) {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      .floating-icon:nth-child(2) {
        top: 60%;
        right: 15%;
        animation-delay: 1.5s;
      }

      .floating-icon:nth-child(3) {
        bottom: 30%;
        left: 20%;
        animation-delay: 3s;
      }

      .floating-icon:nth-child(4) {
        top: 40%;
        right: 25%;
        animation-delay: 4.5s;
      }

      @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
      }

      .card-content {
        position: relative;
        z-index: 2;
        text-align: center;
        color: #000;
      }

      .card-header {
        margin-bottom: 20px;
      }

      .main-icon {
        font-size: 2.2rem;
        margin-bottom: 10px;
        filter: drop-shadow(0 3px 10px rgba(0, 0, 0, 0.2));
        animation: bounce 2s ease-in-out infinite;
      }

      @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
      }

      .card-title {
        font-family: 'Cairo', Arial, sans-serif;
        font-size: 2.1rem;
        font-weight: 900;
        margin: 0 0 10px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        line-height: 1.2;
      }

      .title-decoration {
        width: 60px;
        height: 3px;
        background: rgba(0, 0, 0, 0.2);
        margin: 0 auto;
        border-radius: 2px;
      }

      .card-description {
        font-family: 'Noto Kufi Arabic', Arial, sans-serif;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 25px;
        color: rgba(0, 0, 0, 0.8);
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
      }







      /* الاستجابة للشاشات الصغيرة */
      @media (max-width: 768px) {
        .register-card {
          padding: 30px 20px;
        }

        .card-title {
          font-size: 1.8rem;
        }

        .card-description {
          font-size: 1rem;
        }
      }

      @media (max-width: 480px) {
        .register-cta-section {
          margin: 30px auto;
          padding: 0 10px;
        }

        .register-card {
          padding: 25px 15px;
          border-radius: 15px;
        }

        .card-title {
          font-size: 1.5rem;
        }

        .card-description {
          font-size: 0.95rem;
        }

        .main-icon {
          font-size: 2rem;
        }
      }
    </style>
    <title>موقع تعليمي</title>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="index.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="courses.html">الدورات</a></li>
                <li><a href="contact.html">اتصل بنا</a></li>
                <li><a href="about.html">من نحن</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="index.html" class="login-btn-links">الرئيسية</a></li>
            <li><a href="courses.html" class="login-btn-links">الدورات</a></li>
            <li><a href="contact.html" class="login-btn-links">اتصل بنا</a></li>
            <li><a href="about.html" class="login-btn-links">من نحن</a></li>
        </ul>
        <div class="header-actions">
            <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
            <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
        </div>
    </div>
    
    <main>
        <!-- الجزء الأول: النص والأزرار -->
        <section style="display: flex; align-items: center; justify-content: space-between; padding: 0 120px 80px 120px; margin: 0; max-width: 1400px; margin: 0 auto;">
            <!-- الجزء الأيسر: النص والأزرار -->
            <div style="flex: 1; max-width: 600px; margin-top: 30px;">
                <h1 style="font-size: 2.8rem; font-weight: 800; color: #fff; margin-bottom: 30px; line-height: 1.2; font-family: 'Cairo', Arial, sans-serif; white-space: nowrap;">
                    أفضل مدرسة <span style="color: #48ce1d;">افتراضية</span>
                </h1>

                <p style="font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-weight: 400; font-size: 0.84rem; line-height: 1.8; color: rgba(255,255,255,0.9); margin-bottom: 40px;">
                    منصة دارين لتعليم تهدف إلى تطوير العملية التعليمية ومساعدة الطلاب على تحقيق التفوق في جميع المواد من خلال خدمات تعليمية متكاملة وحديثة.
                </p>

                <div style="display: flex; gap: 20px; flex-wrap: wrap;" class="buttons-container">
                    <button style="background: #48ce1d; color: #191717; border: none; padding: 15px 30px; border-radius: 8px; font-family: 'Cairo', Arial, sans-serif; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(72, 206, 29, 0.3);">
                        طلب حصة مجانية
                    </button>
                    <button style="background: transparent; color: #fff; border: 2px solid #48ce1d; padding: 15px 30px; border-radius: 8px; font-family: 'Cairo', Arial, sans-serif; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s;">
                        تصفح الكورسات
                    </button>
                </div>
            </div>

            <!-- الجزء الأيمن: الصورة -->
            <div style="flex: 1; display: flex; justify-content: center; align-items: flex-end;">
                <img src="../assets/images/ph1.png" alt="صورة تعليمية" style="width: 400px; height: 400px; object-fit: cover; margin-top: 50px;">
            </div>
        </section>
        
        <!-- لماذا منصة دارين؟ -->
        <section class="why-dareen-section">
            <div class="why-dareen-container">
                <h2 class="why-dareen-title" style="color: #fff; margin-bottom: 0;">لماذا منصة دارين؟</h2>
                <p class="why-dareen-desc" style="color: #fff; text-align: center; font-size: 1.13rem; font-family: 'Cairo', Arial, sans-serif; font-weight: 400; margin-top: 8px; margin-bottom: 48px;">اكتشف مميزات منصتنا فهي الخيار الأفضل للطلاب وأولياء الأمور.</p>
                <div class="why-dareen-grid">
                    <!-- ميزة 1 -->
                    <div class="why-dareen-card">
                        <span class="why-dareen-icon">
                            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="22" cy="22" r="22" fill="#48ce1d" fill-opacity="0.13"/>
                                <path d="M32 32V29.5C32 27.0147 29.9853 25 27.5 25H16.5C14.0147 25 12 27.0147 12 29.5V32" stroke="#48ce1d" stroke-width="2.2" stroke-linecap="round"/>
                                <circle cx="22" cy="18" r="5" stroke="#48ce1d" stroke-width="2.2"/>
                            </svg>
                        </span>
                        <div class="why-dareen-card-title">أفضل المدرسين</div>
                        <div class="why-dareen-card-desc">نخبة مدرسين متخصصين في كافة المواد.</div>
                    </div>
                    <!-- ميزة 2 -->
                    <div class="why-dareen-card">
                        <span class="why-dareen-icon">
                            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="22" cy="22" r="22" fill="#48ce1d" fill-opacity="0.13"/>
                                <rect x="13" y="16" width="18" height="12" rx="2" stroke="#48ce1d" stroke-width="2.2"/>
                                <path d="M13 20H31" stroke="#48ce1d" stroke-width="2.2" stroke-linecap="round"/>
                            </svg>
                        </span>
                        <div class="why-dareen-card-title">محتوى تعليمي متكامل</div>
                        <div class="why-dareen-card-desc">كورسات وكتب وامتحانات شاملة تلبي احتياجات الطالب.</div>
                    </div>
                    <!-- ميزة 3 -->
                    <div class="why-dareen-card">
                        <span class="why-dareen-icon">
                            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="22" cy="22" r="22" fill="#48ce1d" fill-opacity="0.13"/>
                                <path d="M22 30L28.6603 33.6603C29.4031 34.0707 30.25 33.5222 30.25 32.6603V19.5" stroke="#48ce1d" stroke-width="2.2" stroke-linecap="round"/>
                                <path d="M22 30L15.3397 33.6603C14.5969 34.0707 13.75 33.5222 13.75 32.6603V19.5" stroke="#48ce1d" stroke-width="2.2" stroke-linecap="round"/>
                                <path d="M22 30V14" stroke="#48ce1d" stroke-width="2.2" stroke-linecap="round"/>
                            </svg>
                        </span>
                        <div class="why-dareen-card-title">نتائج متميزة</div>
                        <div class="why-dareen-card-desc">دعم الطلاب لتحقيق التفوق في جميع المواد.</div>
                    </div>
                    <!-- ميزة 4 -->
                    <div class="why-dareen-card">
                        <span class="why-dareen-icon">
                            <svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="22" cy="22" r="22" fill="#48ce1d" fill-opacity="0.13"/>
                                <path d="M16 28L22 16L28 28" stroke="#48ce1d" stroke-width="2.2" stroke-linecap="round"/>
                                <circle cx="22" cy="31" r="2" fill="#48ce1d"/>
                            </svg>
                        </span>
                        <div class="why-dareen-card-title">تجربة تعليمية حديثة</div>
                        <div class="why-dareen-card-desc">منصة تفاعلية بتقنيات تعليمية حديثة وسهلة الاستخدام.</div>
                    </div>
                </div>
            </div>
        </section>
        <!-- قسم حصص القرآن الكريم بتصميم مشابه لأفضل مدرسة افتراضية مع عكس مكان الصورة -->
        <section class="quran-hero-section" style="display: flex; align-items: center; justify-content: space-between; padding: 0 120px 80px 120px; margin: 0; max-width: 1400px; margin: 0 auto;">
          <!-- الجزء الأيسر: الصورة -->
          <div style="flex: 1; display: flex; justify-content: center; align-items: flex-end;">
            <img src="../assets/images/qur1.png" alt="صورة قرآنية" style="width: 400px; height: 400px; object-fit: cover; margin-top: 50px;">
                </div>
          <!-- الجزء الأيمن: النص والأزرار -->
          <div style="flex: 1; max-width: 600px; margin-top: 30px;">
            <h1 style="font-size: 2.4rem; font-weight: 800; color: #48ce1d; margin-bottom: 30px; line-height: 1.2; font-family: 'Cairo', Arial, sans-serif; white-space: normal;">
              حصص <span style="color: #fff; background: #48ce1d; border-radius: 8px; padding: 0 10px;">القرآن الكريم</span>
            </h1>
            <p style="font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-weight: 400; font-size: 1.1rem; line-height: 1.8; color: rgba(255,255,255,0.9); margin-bottom: 40px;">
              تعلم التلاوة الصحيحة، أحكام التجويد، وحفظ القرآن الكريم مع نخبة من المعلمين المتخصصين لجميع الأعمار والمستويات.
            </p>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;" class="buttons-container">
              <button style="background: #48ce1d; color: #191717; border: none; padding: 15px 30px; border-radius: 8px; font-family: 'Cairo', Arial, sans-serif; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(72, 206, 29, 0.3);">
                احجز الآن
              </button>
              <button style="background: transparent; color: #fff; border: 2px solid #48ce1d; padding: 15px 30px; border-radius: 8px; font-family: 'Cairo', Arial, sans-serif; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s;">
                استفسر عن الحصص
              </button>
            </div>
          </div>
        </section>

        <!-- المواد الدراسية الموجودة (مطابق لمنصة eduvalu.com) -->
        <!-- تم حذف هذا القسم بناءً على طلب المستخدم -->
        <!-- الكتب الأكثر شهرة (مطابق لمنصة eduvalu.com) -->
        <!-- تم حذف هذا القسم بناءً على طلب المستخدم -->
        <!-- كورسات التأسيس المحسنة -->
        <section class="foundation-courses-section" style="padding: 100px 20px; background: linear-gradient(135deg, #232323 0%, #151413 50%, #1a1a1a 100%); position: relative;">
          <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(72, 206, 29, 0.05) 0%, transparent 50%); pointer-events: none;"></div>
          <div class="foundation-courses-container" style="max-width: 1200px; margin: 0 auto; position: relative; z-index: 2;">
            <h2 class="foundation-courses-title" style="color: #fff; margin-bottom: 15px; font-size: 2.5rem; font-weight: 900; text-align: center; font-family: 'Cairo', Arial, sans-serif;">
              كورسات <span style="color: #48ce1d; text-shadow: 0 0 20px rgba(72, 206, 29, 0.5);">التأسيس</span>
            </h2>
            <p style="color: rgba(255,255,255,0.9); text-align: center; font-size: 1.2rem; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-weight: 400; margin-bottom: 60px; max-width: 600px; margin-left: auto; margin-right: auto;">
              ابدأ رحلتك التعليمية مع كورسات التأسيس المتخصصة التي تبني قاعدة قوية في المواد الأساسية.
            </p>
            <div class="foundation-courses-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px; margin-top: 50px;">
              <div class="foundation-course-card why-dareen-card">
                <div class="foundation-course-img">
                  <img src="../assets/images/foundation-arabic.png" alt="اللغة العربية">
                  <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
                </div>
                <div class="foundation-course-name why-dareen-card-title">اللغة العربية</div>
                <div class="foundation-course-details why-dareen-card-desc">12 درس - تأسيس القراءة والكتابة وقواعد النحو</div>
                <div class="foundation-rating-box">
                  <span class="foundation-rating-type">مدفوعة</span>
                  <span class="foundation-rating-value">4.0</span>
                  <span class="foundation-rating-stars">
                    <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                  </span>
                </div>
                <button class="foundation-course-btn">عرض التفاصيل</button>
              </div>
              <div class="foundation-course-card why-dareen-card">
                <div class="foundation-course-img">
                  <img src="../assets/images/foundation-english.png" alt="اللغة الإنجليزية">
                  <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
                </div>
                <div class="foundation-course-name why-dareen-card-title">اللغة الإنجليزية</div>
                <div class="foundation-course-details why-dareen-card-desc">10 دروس - تأسيس القواعد والمحادثة والاستماع</div>
                <div class="foundation-rating-box">
                  <span class="foundation-rating-type">مدفوعة</span>
                  <span class="foundation-rating-value">5.0</span>
                  <span class="foundation-rating-stars">
                    <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span>
                  </span>
                </div>
                <button class="foundation-course-btn">عرض التفاصيل</button>
              </div>
              <div class="foundation-course-card why-dareen-card">
                <div class="foundation-course-img">
                  <img src="../assets/images/foundation-math.png" alt="الرياضيات">
                  <span class="foundation-live-badge"><span class="foundation-live-dot"></span>مباشر</span>
                </div>
                <div class="foundation-course-name why-dareen-card-title">الرياضيات</div>
                <div class="foundation-course-details why-dareen-card-desc">15 درس - أساسيات الحساب والهندسة والتفكير المنطقي</div>
                <div class="foundation-rating-box">
                  <span class="foundation-rating-type">مدفوعة</span>
                  <span class="foundation-rating-value">4.5</span>
                  <span class="foundation-rating-stars">
                    <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">☆</span>
                  </span>
                </div>
                <button class="foundation-course-btn">عرض التفاصيل</button>
              </div>
            </div>
          </div>
        </section>
        <section class="faq-section">
  <div class="faq-container">
    <h2 class="faq-title">الأسئلة الشائعة</h2>
    <p class="faq-desc">إجابات على أكثر الأسئلة شيوعًا حول المنصة والخدمات.</p>
    <div class="faq-list">
      <div class="faq-item">
        <button class="faq-question">كيف يمكنني التسجيل في المنصة؟</button>
        <div class="faq-answer">يمكنك التسجيل بسهولة من خلال الضغط على زر "تسجيل" في أعلى الصفحة وملء البيانات المطلوبة.</div>
      </div>
      <div class="faq-item">
        <button class="faq-question">هل الكورسات متاحة لجميع المراحل ؟</button>
        <div class="faq-answer">نعم، المنصة توفر كورسات لجميع المراحل الدراسية ولكافة المواد.</div>
      </div>
      <div class="faq-item">
        <button class="faq-question">كيف يمكنني التواصل مع المدرسين؟</button>
        <div class="faq-answer">يمكنك التواصل مع المدرسين من خلال صفحة الكورس أو عبر الرسائل داخل المنصة.</div>
      </div>
      <div class="faq-item">
        <button class="faq-question">هل يوجد دعم فني؟</button>
        <div class="faq-answer">نعم، يوجد فريق دعم فني جاهز لمساعدتك في أي وقت عبر صفحة "اتصل بنا".</div>
      </div>
    </div>
  </div>
</section>
<section class="virtual-school-section">
  <div class="virtual-school-container">
    <div class="virtual-school-image">
      <img src="../assets/images/Dr3.png" alt="أفضل مدرسة افتراضية">
    </div>
    <div class="virtual-school-content">
      <h2 class="virtual-school-title">مستعد للنجاح؟</h2>
      <p class="virtual-school-desc">
        منصة دارين هي منصتك الأولى لتحقيق التفوق في جميع المواد. سجّل الآن وابدأ رحلتك نحو النجاح!
      </p>
      <a href="register.html" class="virtual-school-btn">انشئ حسابك الآن</a>
    </div>
  </div>
</section>
<script>
document.querySelectorAll('.faq-question').forEach(btn => {
  btn.addEventListener('click', function() {
    const item = this.parentElement;
    item.classList.toggle('active');
    // لإغلاق باقي الأسئلة عند فتح واحد فقط، أزل التعليق عن الأسطر التالية:
    // document.querySelectorAll('.faq-item').forEach(faq => {
    //   if(faq !== item) faq.classList.remove('active');
    // });
  });
});
</script>
    </main>

    <!-- قسم التسجيل المحسن -->
    <section class="register-cta-section">
      <div class="register-card">
        <div class="card-background">
          <div class="floating-elements">
            <div class="floating-icon">🎓</div>
            <div class="floating-icon">📚</div>
            <div class="floating-icon">⭐</div>
            <div class="floating-icon">🚀</div>
          </div>
        </div>

        <div class="card-content">
          <div class="card-header">
            <div class="main-icon">🎯</div>
            <h2 class="card-title">ابدأ رحلتك التعليمية الآن!</h2>
            <div class="title-decoration"></div>
          </div>

          <p class="card-description">
            انضم إلى آلاف الطلاب المتفوقين واحصل على وصول فوري لجميع الدورات والمحتوى الحصري مع أفضل المدرسين المتخصصين.
          </p>






        </div>
      </div>
    </section>


    <!-- شريط أيقونات التواصل الاجتماعي الجانبي (صور من مجلد images) -->
    <div class="social-sidebar">
      <a href="#" class="social-icon" target="_blank" title="انستجرام">
        <img src="../assets/images/instagram.png" alt="انستجرام" class="social-img">
      </a>
      <a href="#" class="social-icon" target="_blank" title="تيك توك">
        <img src="../assets/images/tiktok.png" alt="تيك توك" class="social-img">
      </a>
      <a href="#" class="social-icon" target="_blank" title="تليجرام">
        <img src="../assets/images/telegram.png" alt="تليجرام" class="social-img">
      </a>
    </div>

    <button class="back-to-top" title="الصعود للأعلى">
      <svg width="22" height="22" viewBox="0 0 24 24" fill="#48ce1d" xmlns="http://www.w3.org/2000/svg">
        <polygon points="12,6 18,16 6,16" />
      </svg>
    </button>

    <script>
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }
    </script>
    <script>
window.addEventListener('scroll', function() {
  var btn = document.querySelector('.back-to-top');
  if (window.scrollY > 200) {
    btn.classList.add('show');
  } else {
    btn.classList.remove('show');
  }
});
document.querySelector('.back-to-top').onclick = function() {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};
</script>
<script>
// تحسين تفاعل القائمة البريدية
document.addEventListener('DOMContentLoaded', function() {
  const newsletterForm = document.querySelector('.newsletter-hero-form-content form');
  const newsletterInput = document.querySelector('.newsletter-hero-input');
  const countrySelect = document.querySelector('.newsletter-country-select');
  const newsletterBtn = document.querySelector('.newsletter-hero-btn');
  const newsletterFormContainer = document.querySelector('.newsletter-hero-form');

  if (newsletterForm && newsletterInput && newsletterBtn) {
    // تحسين تجربة المستخدم عند الكتابة
    newsletterInput.addEventListener('input', function() {
      if (this.value.length > 0) {
        this.style.borderColor = '#48ce1d';
        this.style.boxShadow = '0 0 20px rgba(72, 206, 29, 0.3)';
      } else {
        this.style.borderColor = 'rgba(72, 206, 29, 0.4)';
        this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
      }
    });

    // معالجة إرسال النموذج
    newsletterForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const phoneNumber = newsletterInput.value.trim();
      const countryCode = countrySelect.value;
      
      if (!phoneNumber || !isValidPhoneNumber(phoneNumber)) {
        showError('يرجى إدخال رقم هاتف صحيح');
        return;
      }

      // إظهار حالة التحميل
      newsletterBtn.classList.add('loading');
      newsletterBtn.innerHTML = '<span>جاري الإرسال...</span>';
      newsletterBtn.disabled = true;

      // محاكاة إرسال البيانات
      setTimeout(() => {
        newsletterBtn.classList.remove('loading');
        newsletterFormContainer.classList.add('success');
        
        // إظهار رسالة النجاح
        showSuccess('تم الاشتراك بنجاح! 🎉');
        
        // إعادة تعيين النموذج
        newsletterInput.value = '';
        newsletterBtn.innerHTML = '<span>اشتراك مجاني</span><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
        newsletterBtn.disabled = false;
        
        // إزالة حالة النجاح بعد 3 ثوان
        setTimeout(() => {
          newsletterFormContainer.classList.remove('success');
        }, 3000);
      }, 2000);
    });

    // تحسين تجربة التركيز
    newsletterInput.addEventListener('focus', function() {
      this.parentElement.style.transform = 'scale(1.02)';
    });

    newsletterInput.addEventListener('blur', function() {
      this.parentElement.style.transform = 'scale(1)';
    });

      // تأثيرات إضافية للزر
  newsletterBtn.addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-4px) scale(1.02)';
  });

  newsletterBtn.addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0) scale(1)';
  });

}

  // دالة التحقق من صحة رقم الهاتف
  function isValidPhoneNumber(phone) {
    const phoneRegex = /^[0-9]{8,10}$/;
    return phoneRegex.test(phone);
  }

  // دالة إظهار رسالة الخطأ
  function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'newsletter-error';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
      position: absolute;
      top: -40px;
      left: 0;
      right: 0;
      background: #ff4444;
      color: white;
      padding: 10px;
      border-radius: 8px;
      font-size: 0.9rem;
      text-align: center;
      animation: slideDown 0.3s ease-out;
    `;
    
    const formContainer = document.querySelector('.newsletter-input-group');
    formContainer.style.position = 'relative';
    formContainer.appendChild(errorDiv);
    
    setTimeout(() => {
      errorDiv.remove();
    }, 3000);
  }

  // دالة إظهار رسالة النجاح
  function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'newsletter-success';
    successDiv.textContent = message;
    successDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #48ce1d;
      color: white;
      padding: 15px 20px;
      border-radius: 10px;
      font-size: 1rem;
      font-weight: 600;
      box-shadow: 0 4px 15px rgba(72, 206, 29, 0.3);
      z-index: 1000;
      animation: slideInRight 0.5s ease-out;
    `;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
      successDiv.style.animation = 'slideOutRight 0.5s ease-out';
      setTimeout(() => {
        successDiv.remove();
      }, 500);
    }, 3000);
  }
});

// إضافة تأثيرات CSS للرسائل
const style = document.createElement('style');
style.textContent = `
  @keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(style);
</script>

<footer class="modern-footer">
  <div class="footer-wrapper">


    <!-- القسم الأوسط المنظم -->
    <div class="footer-middle">
      <div class="footer-grid">
        <!-- الروابط السريعة -->
        <div class="footer-column">
          <h3 class="column-title">
            <span class="title-icon">🔗</span>
            الروابط السريعة
          </h3>
          <nav class="footer-nav">
            <a href="index.html" class="nav-link">
              <span class="link-icon">🏠</span>
              الرئيسية
            </a>
            <a href="courses.html" class="nav-link">
              <span class="link-icon">📖</span>
              الدورات
            </a>
            <a href="about.html" class="nav-link">
              <span class="link-icon">ℹ️</span>
              من نحن
            </a>
            <a href="contact.html" class="nav-link">
              <span class="link-icon">📞</span>
              اتصل بنا
            </a>
          </nav>
        </div>

        <!-- الخدمات التعليمية -->
        <div class="footer-column">
          <h3 class="column-title">
            <span class="title-icon">🎯</span>
            خدماتنا التعليمية
          </h3>
          <nav class="footer-nav">
            <a href="#" class="nav-link">
              <span class="link-icon">🌟</span>
              كورسات التأسيس
            </a>
            <a href="#" class="nav-link">
              <span class="link-icon">📿</span>
              حصص القرآن الكريم
            </a>
            <a href="#" class="nav-link">
              <span class="link-icon">🧠</span>
              دورات القدرات
            </a>
            <a href="#" class="nav-link">
              <span class="link-icon">📝</span>
              المراجعات النهائية
            </a>
          </nav>
        </div>

        <!-- وسائل التواصل الاجتماعي -->
        <div class="footer-column">
          <h3 class="column-title">
            <span class="title-icon">🌐</span>
            تابعنا على
          </h3>
          <div class="social-section">
            <div class="social-grid">
              <a href="#" class="social-item" title="انستجرام">
                <img src="../assets/images/instagram.png" alt="انستجرام">
                <span class="social-name">Instagram</span>
              </a>
              <a href="#" class="social-item" title="تيك توك">
                <img src="../assets/images/tiktok.png" alt="تيك توك">
                <span class="social-name">TikTok</span>
              </a>
              <a href="#" class="social-item" title="تليجرام">
                <img src="../assets/images/telegram.png" alt="تليجرام">
                <span class="social-name">Telegram</span>
              </a>
            </div>
          </div>

          <!-- الروابط القانونية -->
          <div class="legal-section">
            <h4 class="legal-heading">روابط مهمة</h4>
            <div class="legal-links-footer">
              <a href="#" onclick="alert('سيتم إضافة سياسة الخصوصية لاحقًا')" class="legal-link-footer">سياسة الخصوصية</a>
              <a href="#" onclick="alert('سيتم إضافة الشروط لاحقًا')" class="legal-link-footer">الشروط والأحكام</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- القسم السفلي الأنيق -->
    <div class="footer-bottom-modern">
      <div class="bottom-content">
        <div class="copyright-modern">
          <p>جميع الحقوق محفوظة لـ معهد دارين</p>
        </div>
      </div>
    </div>
  </div>
</footer>

<style>
/* فوتر حديث ومرتب */
.modern-footer {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
  color: #fff;
  margin-top: 80px;
  font-family: 'Cairo', Arial, sans-serif;
  position: relative;
  overflow: hidden;
}

.modern-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(72, 206, 29, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(72, 206, 29, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(72, 206, 29, 0.02) 50%, transparent 70%);
  pointer-events: none;
}

.footer-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}





/* القسم الأوسط المنظم */
.footer-middle {
  padding: 80px 20px 60px;
  background: rgba(0, 0, 0, 0.2);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 50px;
  max-width: 1000px;
  margin: 0 auto;
}

.footer-column {
  position: relative;
}

.column-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.3rem;
  font-weight: 700;
  color: #48ce1d;
  margin: 0 0 25px 0;
  position: relative;
}

.column-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  right: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #48ce1d, #3eff00);
  border-radius: 2px;
}

.title-icon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 5px rgba(72, 206, 29, 0.5));
}

/* التنقل الحديث */
.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 12px 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(72, 206, 29, 0.1), transparent);
  transition: right 0.5s ease;
}

.nav-link:hover::before {
  right: 100%;
}

.nav-link:hover {
  color: #48ce1d;
  background: rgba(72, 206, 29, 0.1);
  transform: translateX(-5px);
}

.link-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.nav-link:hover .link-icon {
  transform: scale(1.2);
}
/* معلومات الاتصال الحديثة */
.contact-modern {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(72, 206, 29, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.contact-card:hover {
  background: rgba(72, 206, 29, 0.1);
  border-color: rgba(72, 206, 29, 0.3);
  transform: translateY(-2px);
}

.contact-icon-wrapper {
  width: 45px;
  height: 45px;
  background: rgba(72, 206, 29, 0.15);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
}

.contact-details {
  flex: 1;
}

.contact-label {
  display: block;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 3px;
}

.contact-link {
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: #48ce1d;
}

.contact-text {
  color: #fff;
  font-weight: 600;
  font-size: 1rem;
}

/* النشرة الإخبارية */
.newsletter-section {
  margin-bottom: 30px;
}

.newsletter-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.newsletter-form {
  display: flex;
  gap: 10px;
  margin-bottom: 25px;
}

.newsletter-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid rgba(72, 206, 29, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  font-family: 'Cairo', Arial, sans-serif;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.newsletter-input:focus {
  outline: none;
  border-color: #48ce1d;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 20px rgba(72, 206, 29, 0.2);
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.newsletter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
  color: #000;
  border: none;
  border-radius: 10px;
  font-weight: 700;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', Arial, sans-serif;
}

.newsletter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(72, 206, 29, 0.4);
}

.btn-icon {
  font-size: 1rem;
}

/* وسائل التواصل الحديثة */
.social-section {
  margin-top: 25px;
}

.social-heading {
  font-size: 1rem;
  color: #48ce1d;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.social-grid {
  display: flex;
  gap: 12px;
}

.social-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(72, 206, 29, 0.1);
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
  flex: 1;
}

.social-item:hover {
  background: rgba(72, 206, 29, 0.1);
  border-color: rgba(72, 206, 29, 0.3);
  transform: translateY(-3px);
}

.social-item img {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.social-item:hover img {
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(72, 206, 29, 0.4);
}

.social-name {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.social-item:hover .social-name {
  color: #48ce1d;
}

/* الروابط القانونية في الفوتر */
.legal-section {
  margin-top: 30px;
}

.legal-heading {
  font-size: 1rem;
  color: #48ce1d;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.legal-links-footer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legal-link-footer {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(72, 206, 29, 0.1);
}

.legal-link-footer:hover {
  color: #48ce1d;
  background: rgba(72, 206, 29, 0.1);
  border-color: rgba(72, 206, 29, 0.3);
  transform: translateX(-3px);
}

/* القسم السفلي الأنيق */
.footer-bottom-modern {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(72, 206, 29, 0.15);
  padding: 25px 20px;
}

.bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright-modern p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
}





/* الاستجابة للشاشات المختلفة */
@media (max-width: 1200px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .footer-wrapper {
    padding: 0 15px;
  }

  .footer-middle {
    padding: 60px 15px 40px;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .column-title::after {
    left: 50%;
    transform: translateX(-50%);
    right: auto;
  }

  .nav-link:hover {
    transform: none;
  }

  .social-grid {
    justify-content: center;
  }

  .bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .footer-middle {
    padding: 40px 10px 30px;
  }

  .footer-grid {
    gap: 30px;
  }

  .column-title {
    font-size: 1.1rem;
  }

  .nav-link {
    padding: 10px 12px;
    font-size: 0.95rem;
  }

  .social-grid {
    gap: 8px;
  }

  .social-item {
    padding: 6px;
  }

  .social-item img {
    width: 12px;
    height: 12px;
  }

  .social-name {
    font-size: 0.65rem;
  }

  .footer-bottom-modern {
    padding: 20px 10px;
  }
}

/* تحسينات إضافية للأداء */
@media (prefers-reduced-motion: reduce) {
  .modern-footer *,
  .modern-footer *::before,
  .modern-footer *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

  .social-grid {
    justify-content: center;
  }

  .bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .legal-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-top {
    padding: 40px 10px 30px;
  }

  .modern-logo {
    width: 60px;
    height: 60px;
  }

  .brand-name {
    font-size: 1.8rem;
  }

  .brand-tagline {
    font-size: 1rem;
  }

  .brand-description {
    font-size: 1rem;
  }

  .achievement-badges {
    gap: 15px;
  }

  .badge {
    min-width: 200px;
    padding: 15px 20px;
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .badge-icon {
    font-size: 1.5rem;
  }

  .badge-number {
    font-size: 1.5rem;
  }

  .footer-middle {
    padding: 30px 10px;
  }

  .footer-grid {
    gap: 30px;
  }

  .column-title {
    font-size: 1.1rem;
  }

  .nav-link {
    padding: 10px 12px;
    font-size: 0.95rem;
  }

  .contact-card {
    padding: 12px;
  }

  .contact-icon-wrapper {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .newsletter-input,
  .newsletter-btn {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  .social-grid {
    gap: 8px;
  }

  .social-item {
    padding: 10px 8px;
  }

  .social-item img {
    width: 25px;
    height: 25px;
  }

  .social-name {
    font-size: 0.75rem;
  }

  .footer-bottom-modern {
    padding: 20px 10px;
  }

  .legal-links {
    flex-direction: column;
    gap: 10px;
  }

  .separator {
    display: none;
  }
}

/* تحسينات إضافية للأداء */
@media (prefers-reduced-motion: reduce) {
  .modern-footer *,
  .modern-footer *::before,
  .modern-footer *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* تحسين الطباعة */
@media print {
  .modern-footer {
    background: #fff !important;
    color: #000 !important;
  }

  .modern-footer * {
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }
}
</style>
</style>
</style>


