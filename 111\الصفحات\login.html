<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/style.css">
    <title>تسجيل الدخول - دارين لتعليم</title>
</head>
<body style="background: linear-gradient(135deg, #151413 0%, #1a1a1a 50%, #232323 100%); min-height: 100vh; margin: 0; padding: 0;">
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="index.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="courses.html">الدورات</a></li>
                <li><a href="contact.html">اتصل بنا</a></li>
                <li><a href="about.html">من نحن</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="index.html" class="login-btn-links">الرئيسية</a></li>
            <li><a href="courses.html" class="login-btn-links">الدورات</a></li>
            <li><a href="contact.html" class="login-btn-links">اتصل بنا</a></li>
            <li><a href="about.html" class="login-btn-links">من نحن</a></li>
        </ul>
        <div class="header-actions" style="display: flex; flex-direction: column; gap: 10px; align-items: center;">
            <button class="action-btn login" style="width: 200px;" onclick="window.location.href='login.html'">تسجيل دخول</button>
            <button class="action-btn subscribe" style="width: 200px;" onclick="window.location.href='register.html'">اشــتراك</button>
            <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
        </div>
    </div>

    <main>
        <div style="display: flex; align-items: center; gap: 60px; max-width: 1000px; width: 100%;">
            <!-- قسم الصورة -->
            <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
                <img src="../assets/images/logo.png" alt="شعار دارين" style="width: 400px; height: 400px; object-fit: cover; object-position: center;">
            </div>

            <!-- قسم النموذج -->
            <div style="flex: 0.8; padding: 32px; background: rgba(35, 35, 35, 0.95); border-radius: 19px; box-shadow: 0 6px 26px rgba(0, 0, 0, 0.3); backdrop-filter: blur(10px); border: 1px solid rgba(72, 206, 29, 0.1);">
                <div style="text-align: center; margin-bottom: 19px;">
                    <h1 style="color: #48ce1d; font-family: 'Cairo', Arial, sans-serif; font-weight: 900; font-size: 1.6rem; margin: 0 0 5px 0;">تسجيل الدخول</h1>
                    <p style="color: #fff; opacity: 0.8; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.76rem; margin: 0;">أدخل بياناتك للوصول إلى معهد دارين</p>
                </div>

                <form style="display: flex; flex-direction: column; gap: 13px;">
                    <div>
                        <label for="username" style="display: block; color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.72rem; margin-bottom: 5px; opacity: 0.9;">اسم المستخدم</label>
                        <input type="text" id="username" name="username" required style="width: 100%; padding: 13px 14px; border-radius: 10px; border: 2px solid rgba(72, 206, 29, 0.2); background: rgba(255, 255, 255, 0.05); color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.8rem; outline: none; transition: border-color 0.3s, box-shadow 0.3s; box-sizing: border-box;" placeholder="أدخل اسم المستخدم">
                    </div>

                    <div>
                        <label for="password" style="display: block; color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.72rem; margin-bottom: 5px; opacity: 0.9;">كلمة المرور</label>
                        <input type="password" id="password" name="password" required style="width: 100%; padding: 13px 14px; border-radius: 10px; border: 2px solid rgba(72, 206, 29, 0.2); background: rgba(255, 255, 255, 0.05); color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.8rem; outline: none; transition: border-color 0.3s, box-shadow 0.3s; box-sizing: border-box;" placeholder="أدخل كلمة المرور">
                    </div>

                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 5px;">
                        <label style="display: flex; align-items: center; gap: 6px; color: #fff; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.68rem; opacity: 0.8; cursor: pointer;">
                            <input type="checkbox" style="width: 13px; height: 13px; accent-color: #48ce1d;">
                            تذكرني
                        </label>
                        <a href="#" style="color: #48ce1d; text-decoration: none; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.68rem; transition: opacity 0.3s;">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" style="background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%); color: #191717; font-family: 'Cairo', Arial, sans-serif; font-weight: bold; font-size: 0.88rem; padding: 13px 18px; border-radius: 10px; border: none; cursor: pointer; transition: transform 0.2s, box-shadow 0.2s; margin-top: 5px; width: 160px; margin-left: auto; margin-right: auto; display: block;">تسجيل الدخول</button>

                    <div style="text-align: center; margin-top: 12px;">
                        <p style="color: #fff; opacity: 0.7; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.68rem; margin: 0;">ليس لديك حساب؟
                            <a href="register.html" style="color: #48ce1d; text-decoration: none; font-weight: bold; transition: opacity 0.3s;">إنشاء حساب جديد</a>
                        </p>
                    </div>
                </form>

                <div style="margin-top: 12px; text-align: center;">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 13px; margin-bottom: 10px;">
                        <div style="flex: 1; height: 1px; background: rgba(255, 255, 255, 0.2);"></div>
                        <span style="color: #fff; opacity: 0.6; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.64rem;">أو</span>
                        <div style="flex: 1; height: 1px; background: rgba(255, 255, 255, 0.2);"></div>
                    </div>

                    <div style="display: flex; justify-content: center;">
                        <button style="width: 100%; background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); color: #fff; padding: 10px; border-radius: 6px; font-family: 'Noto Kufi Arabic', Arial, sans-serif; font-size: 0.72rem; cursor: pointer; transition: background 0.3s; display: flex; align-items: center; justify-content: center;">
                            طلب المساعدة من مسؤول المنصة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <style>
        /* إزالة التمرير وضبط ارتفاع الصفحة */
        html, body {
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
        }

        main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 80px); /* ارتفاع الشاشة ناقص ارتفاع الهيدر */
            padding: 20px 40px;
            box-sizing: border-box;
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            main {
                padding: 10px !important;
            }

            main > div {
                flex-direction: column !important;
                gap: 30px !important;
                max-width: 100% !important;
            }

            main > div > div:first-child {
                order: 2;
            }

            main > div > div:first-child img {
                width: 200px !important;
                height: 200px !important;
            }

            main > div > div:last-child {
                order: 1;
                padding: 30px 20px !important;
                margin: 0 10px;
            }
        }

        @media (max-width: 480px) {
            main > div > div:first-child img {
                width: 150px !important;
                height: 150px !important;
            }

            main > div > div:last-child {
                padding: 25px 15px !important;
                margin: 0 5px;
            }

            main > div > div:last-child h1 {
                font-size: 1.5rem !important;
            }
        }
    </style>

    <script>
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }

        // إضافة تأثيرات للحقول عند التركيز
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.borderColor = '#48ce1d';
                    this.style.boxShadow = '0 0 0 3px rgba(72, 206, 29, 0.1)';
                });
                
                input.addEventListener('blur', function() {
                    this.style.borderColor = 'rgba(72, 206, 29, 0.2)';
                    this.style.boxShadow = 'none';
                });
            });

            // تأثير زر تسجيل الدخول
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(72, 206, 29, 0.3)';
            });

            submitBtn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });

            // معالجة تسجيل الدخول
            const loginForm = document.querySelector('form');
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                // التحقق من بيانات مدير النظام
                if (username === 'dareen.kw' && password === '1999') {
                    // تسجيل دخول مدير النظام
                    localStorage.setItem('adminLoggedIn', 'true');
                    localStorage.setItem('adminUsername', 'dareen.kw');
                    window.location.href = 'admin.html';
                } else {
                    // بيانات خاطئة
                    alert('اسم المستخدم أو كلمة المرور غير صحيحة');
                }
            });
        });
    </script>
</body>
</html>
