<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اشتراك - معهد دارين</title>
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Noto+Kufi+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* إزالة التمرير وضبط ارتفاع الصفحة */
        html, body {
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
        }

        main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 80px); /* ارتفاع الشاشة ناقص ارتفاع الهيدر */
            padding: 20px 40px;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 25% 25%, rgba(72, 206, 29, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(72, 206, 29, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }
        
        .container {
            position: relative;
            z-index: 2;
        }
        
        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            main {
                padding: 15px 5px !important;
                min-height: calc(100vh - 70px) !important;
            }

            main > div {
                flex-direction: column !important;
                gap: 20px !important;
                max-width: 100% !important;
                width: 100% !important;
            }

            main > div > div:first-child {
                order: 2;
            }

            main > div > div:first-child img {
                width: 180px !important;
                height: 180px !important;
            }

            main > div > div:last-child {
                order: 1;
                padding: 35px 25px !important;
                margin: 0 !important;
                width: 100% !important;
                max-width: none !important;
            }
        }

        @media (max-width: 480px) {
            main {
                padding: 10px 3px !important;
            }

            main > div > div:first-child img {
                width: 120px !important;
                height: 120px !important;
            }

            main > div > div:last-child {
                padding: 30px 20px !important;
                margin: 0 !important;
                border-radius: 20px !important;
            }

            main > div > div:last-child h1 {
                font-size: 1.6rem !important;
            }

            main > div > div:last-child p {
                font-size: 0.95rem !important;
            }

            /* تحسين الحقول للهواتف */
            main > div > div:last-child input,
            main > div > div:last-child select {
                padding: 16px 18px !important;
                font-size: 1rem !important;
            }

            main > div > div:last-child label {
                font-size: 1rem !important;
                font-weight: 600 !important;
            }

            /* تحسين زر الإرسال */
            main > div > div:last-child button[type="submit"] {
                padding: 18px !important;
                font-size: 1.1rem !important;
                margin-top: 15px !important;
            }
        }

        @media (max-width: 360px) {
            main {
                padding: 8px 2px !important;
            }

            main > div > div:last-child {
                padding: 25px 15px !important;
                border-radius: 18px !important;
            }

            main > div > div:last-child h1 {
                font-size: 1.4rem !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- رأس الموقع -->
        <header>
            <div class="header-container">
                <div class="logo">
                    <a href="index.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
                </div>
                <ul class="nav">
                    <li><a href="index.html">الرئيسية</a></li>
                    <li><a href="courses.html">الدورات</a></li>
                    <li><a href="contact.html">اتصل بنا</a></li>
                    <li><a href="about.html">من نحن</a></li>
                </ul>
                <div class="spacer"></div>
                <div class="header-actions">
                    <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                    <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
                </div>
                <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
            </div>
        </header>
        <div class="mobile-menu" id="mobileMenu">
            <ul class="nav">
                <li><a href="index.html" class="login-btn-links">الرئيسية</a></li>
                <li><a href="courses.html" class="login-btn-links">الدورات</a></li>
                <li><a href="contact.html" class="login-btn-links">اتصل بنا</a></li>
                <li><a href="about.html" class="login-btn-links">من نحن</a></li>
            </ul>
            <div class="header-actions">
                <button class="action-btn login" onclick="window.location.href='login.html'">تسجيل دخول</button>
                <button class="action-btn subscribe" onclick="window.location.href='register.html'">اشــتراك</button>
                <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <main>
            <div style="display: flex; align-items: center; gap: 60px; max-width: 1000px; width: 100%;">
                <!-- قسم الصورة -->
                <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
                    <img src="../assets/images/logo.png" alt="شعار دارين" style="width: 400px; height: 400px; object-fit: cover; object-position: center;">
                </div>
                
                <!-- قسم النموذج -->
                <div style="flex: 0.8; padding: 32px; background: rgba(35, 35, 35, 0.95); border-radius: 19px; box-shadow: 0 6px 26px rgba(0, 0, 0, 0.3); backdrop-filter: blur(10px); border: 1px solid rgba(72, 206, 29, 0.1);">
                    <div style="text-align: center; margin-bottom: 19px;">
                        <h1 style="font-size: 1.6rem; font-weight: 700; color: #48ce1d; margin-bottom: 6px;">إنشاء حساب جديد</h1>
                        <p style="color: rgba(255,255,255,0.7); font-size: 0.8rem;">انضم إلى معهد دارين</p>
                    </div>

                    <form id="registerForm" style="display: flex; flex-direction: column; gap: 16px;">
                        <!-- حقل الاسم -->
                        <div style="display: flex; flex-direction: column; gap: 6px;">
                            <label for="fullName" style="color: rgba(255,255,255,0.9); font-weight: 500; font-size: 0.76rem;">الاسم الكامل</label>
                            <input type="text" id="fullName" name="fullName" required
                                   style="padding: 11px 13px; border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 10px; background: rgba(255,255,255,0.05); color: #fff; font-size: 0.8rem; font-family: 'Cairo', Arial, sans-serif; transition: all 0.3s;"
                                   placeholder="أدخل اسمك الكامل">
                        </div>

                        <!-- حقل رقم الهاتف -->
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label for="phone" style="color: rgba(255,255,255,0.9); font-weight: 500; font-size: 0.95rem;">رقم الهاتف</label>
                            <div style="display: flex; align-items: center; border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 12px; background: rgba(255,255,255,0.05); overflow: hidden;">
                                <input type="tel" id="phone" name="phone" required
                                       style="flex: 1; padding: 14px 16px; border: none; background: transparent; color: #fff; font-size: 1rem; font-family: 'Cairo', Arial, sans-serif; outline: none;"
                                       placeholder="12345678" maxlength="8" pattern="[0-9]{8}">
                                <div style="padding: 14px 16px; background: rgba(72, 206, 29, 0.1); color: #48ce1d; font-weight: 600; border-right: 1px solid rgba(72, 206, 29, 0.2);">
                                    🇰🇼 +965
                                </div>
                            </div>
                        </div>

                        <!-- حقل المرحلة -->
                        <div style="display: flex; flex-direction: column; gap: 8px;">
                            <label for="grade" style="color: rgba(255,255,255,0.9); font-weight: 500; font-size: 0.95rem;">المرحلة الدراسية</label>
                            <select id="grade" name="grade" required 
                                    style="padding: 14px 16px; border: 1px solid rgba(72, 206, 29, 0.2); border-radius: 12px; background: rgba(255,255,255,0.05); color: #fff; font-size: 1rem; font-family: 'Cairo', Arial, sans-serif; transition: all 0.3s;">
                                <option value="" style="background: #2a2a2a; color: #fff;">اختر المرحلة الدراسية</option>
                                <option value="elementary" style="background: #2a2a2a; color: #fff;">المرحلة الابتدائية</option>
                                <option value="middle" style="background: #2a2a2a; color: #fff;">المرحلة المتوسطة</option>
                                <option value="high" style="background: #2a2a2a; color: #fff;">المرحلة الثانوية</option>
                                <option value="university" style="background: #2a2a2a; color: #fff;">المرحلة الجامعية</option>
                            </select>
                        </div>

                        <!-- زر الاشتراك -->
                        <button type="submit" 
                                style="background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%); color: #000; border: none; padding: 16px; border-radius: 12px; font-size: 1.1rem; font-weight: 700; cursor: pointer; transition: all 0.3s; margin-top: 10px; font-family: 'Cairo', Arial, sans-serif;">
                            إنشاء الحساب
                        </button>

                        <!-- رابط تسجيل الدخول -->
                        <div style="text-align: center; margin-top: 16px;">
                            <span style="color: rgba(255,255,255,0.7); font-size: 0.95rem;">لديك حساب بالفعل؟</span>
                            <a href="login.html" style="color: #48ce1d; text-decoration: none; font-weight: 600; margin-right: 8px;">سجل الدخول</a>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للحقول عند التركيز
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.borderColor = '#48ce1d';
                    this.style.boxShadow = '0 0 0 3px rgba(72, 206, 29, 0.1)';
                });
                
                input.addEventListener('blur', function() {
                    this.style.borderColor = 'rgba(72, 206, 29, 0.2)';
                    this.style.boxShadow = 'none';
                });
            });

            // تأثير زر الاشتراك
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 12px rgba(72, 206, 29, 0.3)';
            });
            
            submitBtn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });

            // التحقق من صحة رقم الهاتف الكويتي
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, ''); // إزالة أي شيء غير رقمي
                if (value.length > 8) {
                    value = value.substring(0, 8);
                }
                this.value = value;
            });

            // معالجة إرسال النموذج
            document.getElementById('registerForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const fullName = document.getElementById('fullName').value;
                const phone = document.getElementById('phone').value;
                const grade = document.getElementById('grade').value;
                
                // التحقق من صحة البيانات
                if (!fullName.trim()) {
                    alert('يرجى إدخال الاسم الكامل');
                    return;
                }
                
                if (phone.length !== 8) {
                    alert('يرجى إدخال رقم هاتف كويتي صحيح (8 أرقام)');
                    return;
                }
                
                if (!grade) {
                    alert('يرجى اختيار المرحلة الدراسية');
                    return;
                }
                
                // هنا يمكن إرسال البيانات إلى الخادم
                showSuccessModal();

                // إعادة تعيين النموذج
                this.reset();
            });
        });

        // دالة إظهار النافذة المنبثقة للنجاح
        function showSuccessModal() {
            // إنشاء النافذة المنبثقة
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
                padding: 40px;
                border-radius: 20px;
                text-align: center;
                max-width: 400px;
                width: 90%;
                border: 2px solid #48ce1d;
                box-shadow: 0 20px 60px rgba(72, 206, 29, 0.3);
                animation: slideIn 0.3s ease;
            `;

            modalContent.innerHTML = `
                <div style="font-size: 4rem; margin-bottom: 20px;">✅</div>
                <h2 style="color: #48ce1d; font-size: 1.8rem; margin-bottom: 15px; font-family: 'Cairo', Arial, sans-serif;">شكراً لك!</h2>
                <p style="color: rgba(255,255,255,0.9); font-size: 1.1rem; line-height: 1.6; margin-bottom: 30px; font-family: 'Cairo', Arial, sans-serif;">
                    تم إرسال طلبك للمسؤول بنجاح<br>
                    سيتم التواصل معك قريباً
                </p>
                <button onclick="closeModal()" style="
                    background: linear-gradient(135deg, #48ce1d 0%, #3eff00 100%);
                    color: #000;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 10px;
                    font-size: 1rem;
                    font-weight: 700;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: 'Cairo', Arial, sans-serif;
                ">حسناً</button>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // إضافة الأنماط للحركات
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { transform: translateY(-50px) scale(0.9); opacity: 0; }
                    to { transform: translateY(0) scale(1); opacity: 1; }
                }
                @keyframes fadeOut {
                    from { opacity: 1; }
                    to { opacity: 0; }
                }
            `;
            document.head.appendChild(style);

            // إضافة تأثير للزر
            const button = modalContent.querySelector('button');
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 5px 15px rgba(72, 206, 29, 0.4)';
            });
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });

            // دالة إغلاق النافذة
            window.closeModal = function() {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(modal);
                    document.head.removeChild(style);
                }, 300);
            };

            // إغلاق عند الضغط خارج النافذة
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }

        // دالة القائمة المحمولة
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }
    </script>
</body>
</html>
