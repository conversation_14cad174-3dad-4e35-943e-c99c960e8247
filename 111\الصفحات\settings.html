<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - لوحة تحكم مدير النظام</title>
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Noto+Kufi+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: #48ce1d;
            text-align: center;
            margin: 40px 0;
            font-weight: 700;
        }
        
        .content-message {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            margin: 60px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <!-- رأس الموقع -->
    <header>
        <div class="header-container">
            <div class="logo">
                <a href="admin.html"><img src="../assets/images/logo.png" alt="شعار معهد دارين" class="logo-img"></a>
            </div>
            <ul class="nav">
                <li><a href="teachers.html">المدرسين</a></li>
                <li><a href="students.html">الطلاب</a></li>
                <li><a href="parents.html">أولياء الأمور</a></li>
                <li><a href="invoices.html">الفواتير</a></li>
                <li><a href="sessions.html">الحصص</a></li>
                <li><a href="attendance.html">الحضور</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
            <div class="spacer"></div>
            <div class="header-actions">
                <button class="action-btn login" onclick="logout()">تسجيل خروج</button>
            </div>
            <button class="menu-btn" aria-label="القائمة" onclick="toggleMobileMenu()">☰</button>
        </div>
    </header>
    <div class="mobile-menu" id="mobileMenu">
        <ul class="nav">
            <li><a href="teachers.html" class="login-btn-links">المدرسين</a></li>
            <li><a href="students.html" class="login-btn-links">الطلاب</a></li>
            <li><a href="parents.html" class="login-btn-links">أولياء الأمور</a></li>
            <li><a href="invoices.html" class="login-btn-links">الفواتير</a></li>
            <li><a href="sessions.html" class="login-btn-links">الحصص</a></li>
            <li><a href="attendance.html" class="login-btn-links">الحضور</a></li>
            <li><a href="settings.html" class="login-btn-links">الإعدادات</a></li>
        </ul>
        <div class="header-actions">
            <button class="action-btn login" onclick="logout()">تسجيل خروج</button>
            <button class="action-btn close-btn" onclick="toggleMobileMenu()">إغلاق</button>
        </div>
    </div>

    <div class="admin-container">
        <h1 class="page-title">⚙️ إعدادات النظام</h1>
        <div class="content-message">
            هذه الصفحة مخصصة لإعدادات النظام والتحكم في الميزات<br>
            سيتم إضافة المحتوى والوظائف قريباً
        </div>
    </div>

    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const isAdminLoggedIn = localStorage.getItem('adminLoggedIn');
            if (!isAdminLoggedIn || isAdminLoggedIn !== 'true') {
                alert('يجب تسجيل الدخول أولاً');
                window.location.href = 'login.html';
                return;
            }
        });

        // دالة تسجيل الخروج المباشر
        function logout() {
            localStorage.removeItem('adminLoggedIn');
            localStorage.removeItem('adminUsername');
            window.location.href = 'login.html';
        }

        // دالة القائمة المحمولة
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }
    </script>
</body>
</html>
